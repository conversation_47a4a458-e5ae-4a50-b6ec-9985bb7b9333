name: ci

on:
  push:
    branches:
      - main
  pull_request:
    branches: [ main ]

jobs:
  pyright:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true

      - name: Set up Python
        run: uv python install

      - name: Install the project
        run: uv sync --all-extras --dev

      - name: Run pyright
        run: uv run pyright
