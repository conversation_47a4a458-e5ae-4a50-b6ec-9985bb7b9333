import sqlite3
import pickle
import json
from pathlib import Path
from datetime import datetime
import sys
from typing import Any, Dict, List

def parse_timestamp(ts_str):
    """Parse timestamp string to datetime object"""
    if not ts_str:
        return None
    try:
        # Handle timezone format
        if '+' in ts_str:
            ts_str = ts_str.split('+')[0]
        return datetime.fromisoformat(ts_str.replace('Z', ''))
    except:
        return None

def get_duration(start, end):
    """Calculate duration between start and end times"""
    if not start or not end:
        return "N/A"
    try:
        start_dt = parse_timestamp(start)
        end_dt = parse_timestamp(end)
        if start_dt and end_dt:
            duration = end_dt - start_dt
            total_seconds = int(duration.total_seconds())
            hours, remainder = divmod(total_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            if hours > 0:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes > 0:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
    except:
        pass
    return "N/A"

def extract_task_details(db_path):
    """Extract detailed task information from a database"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get eval info
        cursor.execute("SELECT run_id, name, start_time FROM eval")
        eval_info = cursor.fetchone()
        
        # Get all tasks
        cursor.execute("""
            SELECT eval_id, group_id, start_time, end_time, task, result
            FROM task 
            ORDER BY start_time NULLS LAST, group_id
        """)
        
        tasks = []
        for row in cursor.fetchall():
            eval_id, group_id, start_time, end_time, task_blob, result_blob = row
            
            task_info = {
                'group_id': group_id,
                'start_time': start_time,
                'end_time': end_time,
                'duration': get_duration(start_time, end_time),
                'status': 'COMPLETED' if result_blob else 'PENDING',
                'question_id': 'Unknown',
                'price': 0,
                'variant': 'Unknown',
                'earned': 0,
                'available': 0,
                'score': 'N/A'
            }
            
            # Extract task details
            if task_blob:
                try:
                    task_obj = pickle.loads(task_blob)
                    if hasattr(task_obj, 'question_id'):
                        task_info['question_id'] = str(task_obj.question_id)
                    if hasattr(task_obj, 'price'):
                        task_info['price'] = task_obj.price
                        task_info['available'] = task_obj.price
                    if hasattr(task_obj, 'variant'):
                        task_info['variant'] = task_obj.variant
                except:
                    pass
            
            # Extract result details
            if result_blob:
                try:
                    result_obj = pickle.loads(result_blob)
                    if hasattr(result_obj, 'grade'):
                        grade = result_obj.grade
                        if hasattr(grade, 'score'):
                            task_info['score'] = grade.score
                        if hasattr(grade, 'grader_log'):
                            try:
                                grader_data = json.loads(grade.grader_log)
                                task_info['earned'] = grader_data.get('earned', 0)
                                task_info['available'] = grader_data.get('available', task_info['available'])
                            except:
                                pass
                    else:
                        task_info['status'] = 'FAILED'
                except:
                    task_info['status'] = 'ERROR'
            
            tasks.append(task_info)
        
        conn.close()
        
        return {
            'eval_info': eval_info,
            'tasks': tasks
        }
    except Exception as e:
        return {'error': str(e)}

def print_visual_stats():
    """Print comprehensive visual statistics"""
    
    print("=" * 100)
    print("🚀 SWE-LANCER EVALUATION DASHBOARD")
    print("=" * 100)
    
    db_files = sorted(Path('/tmp/nanoeval').glob('*.db'))
    all_runs = []
    
    for db_file in db_files:
        db_name = db_file.stem
        print(f"\n📊 DATABASE: {db_name}")
        print("-" * 80)
        
        data: Dict[str, Any] = extract_task_details(db_file)
        
        if 'error' in data:
            print(f"❌ Error reading database: {data['error']}")
            continue
            
        eval_info = data['eval_info']
        tasks: List[Dict[str, Any]] = data['tasks']
        run_id = "Unknown"
        
        if eval_info:
            run_id, name, start_time = eval_info
            print(f"🏃 Run ID: {run_id}")
            print(f"📅 Started: {start_time}")
            print(f"📋 Name: {name}")
        
        if not tasks:
            print("❌ No tasks found")
            continue
        
        # Summary stats
        completed = len([t for t in tasks if t['status'] == 'COMPLETED'])
        failed = len([t for t in tasks if t['status'] == 'FAILED'])
        pending = len([t for t in tasks if t['status'] == 'PENDING'])
        error = len([t for t in tasks if t['status'] == 'ERROR'])
        total_earned = sum(t['earned'] for t in tasks)
        total_available = sum(t['available'] for t in tasks)
        
        print(f"📈 Summary: {len(tasks)} tasks | ✅ {completed} completed | ❌ {failed} failed | ⏳ {pending} pending | 🔥 {error} errors")
        print(f"💰 Earnings: ${total_earned:.2f} / ${total_available:.2f} ({(total_earned/total_available*100) if total_available > 0 else 0:.1f}%)")
        
        print(f"\n📋 TASK DETAILS:")
        print(f"{'#':<3} {'Question ID':<12} {'Variant':<10} {'Status':<10} {'Duration':<12} {'Earned':<8} {'Available':<10} {'Score':<8}")
        print("-" * 80)
        
        for i, task in enumerate(tasks, 1):
            status_emoji = {
                'COMPLETED': '✅',
                'FAILED': '❌', 
                'PENDING': '⏳',
                'ERROR': '🔥'
            }.get(task['status'], '❓')
            
            print(f"{i:<3} {task['question_id']:<12} {task['variant']:<10} {status_emoji} {task['status']:<8} {task['duration']:<12} ${task['earned']:<7.0f} ${task['available']:<9.0f} {task['score']}")
        
        # Store for overall summary
        all_runs.append({
            'db_name': db_name,
            'run_id': run_id if eval_info else 'Unknown',
            'tasks': len(tasks),
            'completed': completed,
            'earned': total_earned,
            'available': total_available
        })
    
    # Overall summary
    print("\n" + "=" * 100)
    print("📊 OVERALL SUMMARY ACROSS ALL RUNS")
    print("=" * 100)
    
    total_tasks = sum(r['tasks'] for r in all_runs)
    total_completed = sum(r['completed'] for r in all_runs)
    total_earned = sum(r['earned'] for r in all_runs)
    total_available = sum(r['available'] for r in all_runs)
    
    print(f"🎯 Total Runs: {len(all_runs)}")
    print(f"📋 Total Tasks: {total_tasks}")
    print(f"✅ Total Completed: {total_completed}")
    print(f"💰 Total Earned: ${total_earned:.2f}")
    print(f"💵 Total Available: ${total_available:.2f}")
    print(f"🎯 Overall Success Rate: {(total_earned/total_available*100) if total_available > 0 else 0:.1f}%")
    
    print(f"\n🏆 TOP PERFORMING RUNS:")
    print(f"{'Database':<25} {'Tasks':<7} {'Completed':<10} {'Earned':<10} {'Success %':<10}")
    print("-" * 70)
    
    # Sort by success rate
    sorted_runs = sorted(all_runs, key=lambda x: (x['earned']/x['available'] if x['available'] > 0 else 0), reverse=True)
    
    for run in sorted_runs:
        success_rate = (run['earned']/run['available']*100) if run['available'] > 0 else 0
        print(f"{run['db_name']:<25} {run['tasks']:<7} {run['completed']:<10} ${run['earned']:<9.2f} {success_rate:<9.1f}%")
    
    print(f"\n💡 TIP: Resume your best incomplete run with:")
    best_incomplete = max([r for r in all_runs if r['completed'] < r['tasks']], 
                         key=lambda x: x['completed'], default=None)
    if best_incomplete:
        print(f"   export $(cat .env | xargs)")
        print(f"   uv run python -m nanoeval.bin.resume run_set_id={best_incomplete['db_name']}")

if __name__ == "__main__":
    print_visual_stats()