
import pandas as pd  # pyright: ignore[reportMissingImports]
import ast
import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class TaskAnalysis:
    question_id: str
    variant: str
    price: float
    title: str
    category: str
    difficulty: str
    complexity_score: int
    proposal_count: int
    has_manager_data: bool
    description_length: int
    platforms_affected: List[str]

class TaskAnalyzer:
    def __init__(self, csv_path: str = "swelancer_tasks.csv"):
        self.csv_path = csv_path
        self.tasks_df: Optional[pd.DataFrame] = None
        self.analyses: List[TaskAnalysis] = []
        
    def load_tasks(self) -> None:
        """Load tasks from CSV file"""
        self.tasks_df = pd.read_csv(self.csv_path)
        if self.tasks_df is not None:
            print(f"Loaded {len(self.tasks_df)} tasks from {self.csv_path}")
        
    def categorize_task(self, title: str, description: str) -> str:
        """Categorize task based on title and description"""
        title_lower = title.lower()
        desc_lower = description.lower()
        
        # UI/UX related
        if any(keyword in title_lower for keyword in ['ui', 'ux', 'display', 'styling', 'css', 'font', 'color', 'button', 'modal', 'animation']):
            return "UI/UX"
        
        # Validation related
        if any(keyword in title_lower for keyword in ['validation', 'validate', 'error message', 'input']):
            return "Validation"
            
        # Navigation/Routing
        if any(keyword in title_lower for keyword in ['navigation', 'redirect', 'routing', 'page', 'screen']):
            return "Navigation"
            
        # Data/API related
        if any(keyword in title_lower for keyword in ['api', 'data', 'fetch', 'request', 'response', 'database']):
            return "Data/API"
            
        # Performance
        if any(keyword in title_lower for keyword in ['performance', 'slow', 'loading', 'optimization']):
            return "Performance"
            
        # Authentication/Security
        if any(keyword in title_lower for keyword in ['auth', 'login', 'security', 'permission']):
            return "Authentication"
            
        # Bug fixes
        if any(keyword in title_lower for keyword in ['bug', 'fix', 'issue', 'crash', 'error']):
            return "Bug Fix"
            
        # Copy/Paste functionality
        if any(keyword in title_lower for keyword in ['copy', 'paste', 'clipboard']):
            return "Copy/Paste"
            
        return "Other"
    
    def calculate_difficulty(self, task_data: Dict[str, Any]) -> tuple[str, int]:
        """Calculate difficulty based on multiple factors"""
        complexity_score = 0
        
        # Price factor (higher price = higher difficulty)
        price = task_data.get('price', 0)
        if price >= 8000:
            complexity_score += 3
        elif price >= 4000:
            complexity_score += 2
        else:
            complexity_score += 1
            
        # Manager data complexity
        manager_data = task_data.get('manager_data')
        if manager_data and manager_data != 'nan':
            try:
                if isinstance(manager_data, str):
                    manager_data = ast.literal_eval(manager_data)
                if isinstance(manager_data, dict):
                    proposal_count = len(manager_data.get('all_proposals', []))
                    if proposal_count > 5:
                        complexity_score += 2
                    elif proposal_count > 3:
                        complexity_score += 1
            except:
                pass
                
        # Description length (longer descriptions often mean more complex issues)
        description = task_data.get('html_description', '')
        if len(description) > 2000:
            complexity_score += 2
        elif len(description) > 1000:
            complexity_score += 1
            
        # Variant complexity
        if task_data.get('variant') == 'swe_manager':
            complexity_score += 1
            
        # Convert to difficulty label
        if complexity_score >= 6:
            return "Expert", complexity_score
        elif complexity_score >= 4:
            return "Hard", complexity_score
        elif complexity_score >= 2:
            return "Medium", complexity_score
        else:
            return "Easy", complexity_score
    
    def extract_platforms(self, description: str) -> List[str]:
        """Extract affected platforms from description"""
        platforms = []
        desc_lower = description.lower()
        
        platform_keywords = {
            'Android': ['android'],
            'iOS': ['ios', 'iphone', 'ipad'],
            'Web': ['web', 'browser', 'chrome', 'safari', 'firefox'],
            'Desktop': ['desktop', 'macos', 'windows'],
            'Mobile': ['mobile']
        }
        
        for platform, keywords in platform_keywords.items():
            if any(keyword in desc_lower for keyword in keywords):
                platforms.append(platform)
                
        return platforms if platforms else ['Unknown']
    
    def analyze_tasks(self):
        """Analyze all tasks and generate analysis data"""
        if self.tasks_df is None:
            self.load_tasks()
        if self.tasks_df is None:
            return
            
        for _, row in self.tasks_df.iterrows():
            # Parse manager data safely
            proposal_count = 0
            has_manager_data = False
            
            if pd.notna(row.get('manager_data')):
                try:
                    manager_data = ast.literal_eval(str(row['manager_data']))
                    if isinstance(manager_data, dict):
                        has_manager_data = True
                        proposal_count = len(manager_data.get('all_proposals', []))
                except:
                    pass
            
            # Get basic info
            title = row.get('title', '')
            description = row.get('html_description', '')
            
            # Analyze the task
            category = self.categorize_task(title, description)
            difficulty, complexity_score = self.calculate_difficulty(row.to_dict())
            platforms = self.extract_platforms(description)
            
            analysis = TaskAnalysis(
                question_id=str(row['question_id']),
                variant=row.get('variant', ''),
                price=row.get('price', 0),
                title=title,
                category=category,
                difficulty=difficulty,
                complexity_score=complexity_score,
                proposal_count=proposal_count,
                has_manager_data=has_manager_data,
                description_length=len(description),
                platforms_affected=platforms
            )
            
            self.analyses.append(analysis)
    
    def generate_statistics(self) -> Dict[str, Any]:
        """Generate statistics about the analyzed tasks"""
        if not self.analyses:
            return {}
            
        stats = {
            'total_tasks': len(self.analyses),
            'by_category': {},
            'by_difficulty': {},
            'by_variant': {},
            'price_stats': {
                'min': min(a.price for a in self.analyses),
                'max': max(a.price for a in self.analyses),
                'avg': sum(a.price for a in self.analyses) / len(self.analyses)
            },
            'complexity_distribution': {},
            'manager_tasks_count': sum(1 for a in self.analyses if a.has_manager_data)
        }
        
        # Category distribution
        for analysis in self.analyses:
            stats['by_category'][analysis.category] = stats['by_category'].get(analysis.category, 0) + 1
            stats['by_difficulty'][analysis.difficulty] = stats['by_difficulty'].get(analysis.difficulty, 0) + 1
            stats['by_variant'][analysis.variant] = stats['by_variant'].get(analysis.variant, 0) + 1
            stats['complexity_distribution'][analysis.complexity_score] = stats['complexity_distribution'].get(analysis.complexity_score, 0) + 1
            
        return stats
    
    def export_analysis(self, output_path: str = "task_analysis_results.csv"):
        """Export analysis results to CSV"""
        if not self.analyses:
            print("No analysis data to export. Run analyze_tasks() first.")
            return
            
        # Convert analyses to DataFrame
        data = []
        for analysis in self.analyses:
            data.append({
                'question_id': analysis.question_id,
                'variant': analysis.variant,
                'price': analysis.price,
                'title': analysis.title,
                'category': analysis.category,
                'difficulty': analysis.difficulty,
                'complexity_score': analysis.complexity_score,
                'proposal_count': analysis.proposal_count,
                'has_manager_data': analysis.has_manager_data,
                'description_length': analysis.description_length,
                'platforms_affected': '|'.join(analysis.platforms_affected)
            })
            
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False)
        print(f"Analysis results exported to {output_path}")
        
        # Also export statistics
        stats = self.generate_statistics()
        stats_path = output_path.replace('.csv', '_statistics.json')
        with open(stats_path, 'w') as f:
            json.dump(stats, f, indent=2)
        print(f"Statistics exported to {stats_path}")
    
    def filter_by_criteria(self,
                          categories: Optional[List[str]] = None,
                          difficulties: Optional[List[str]] = None,
                          min_price: Optional[float] = None,
                          max_price: Optional[float] = None,
                          variants: Optional[List[str]] = None) -> List[str]:
        """Filter tasks by criteria and return list of question IDs"""
        if not self.analyses:
            return []
            
        filtered_ids = []
        
        for analysis in self.analyses:
            # Check all criteria
            if categories and analysis.category not in categories:
                continue
            if difficulties and analysis.difficulty not in difficulties:
                continue
            if min_price and analysis.price < min_price:
                continue
            if max_price and analysis.price > max_price:
                continue
            if variants and analysis.variant not in variants:
                continue
                
            filtered_ids.append(analysis.question_id)
            
        return filtered_ids
    
    def export_filtered_ids(self, 
                           filtered_ids: List[str], 
                           output_path: str = "filtered_task_ids.csv"):
        """Export filtered task IDs to CSV"""
        df = pd.DataFrame({'question_id': filtered_ids})
        df.to_csv(output_path, index=False)
        print(f"Filtered task IDs ({len(filtered_ids)} tasks) exported to {output_path}")

def main():
    """Main function to run the task analyzer"""
    analyzer = TaskAnalyzer()
    
    print("🔍 Starting SWE-Lancer Task Analysis...")
    
    # Load and analyze tasks
    analyzer.load_tasks()
    analyzer.analyze_tasks()
    
    # Export full analysis
    analyzer.export_analysis()
    
    # Generate and display statistics
    stats = analyzer.generate_statistics()
    print("\n📊 Analysis Statistics:")
    print(f"Total tasks: {stats['total_tasks']}")
    print(f"Manager tasks: {stats['manager_tasks_count']}")
    print(f"Price range: ${stats['price_stats']['min']:.0f} - ${stats['price_stats']['max']:.0f}")
    print(f"Average price: ${stats['price_stats']['avg']:.0f}")
    
    print("\nCategory distribution:")
    for category, count in sorted(stats['by_category'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {category}: {count}")
    
    print("\nDifficulty distribution:")
    for difficulty, count in sorted(stats['by_difficulty'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {difficulty}: {count}")
    
    # Example: Export high-value UI/UX tasks
    ui_tasks = analyzer.filter_by_criteria(
        categories=['UI/UX'],
        min_price=2000
    )
    if ui_tasks:
        analyzer.export_filtered_ids(ui_tasks, "high_value_ui_tasks.csv")
    
    # Example: Export expert-level tasks
    expert_tasks = analyzer.filter_by_criteria(
        difficulties=['Expert']
    )
    if expert_tasks:
        analyzer.export_filtered_ids(expert_tasks, "expert_level_tasks.csv")
    
    print("\n✅ Task analysis completed!")

if __name__ == "__main__":
    main()
