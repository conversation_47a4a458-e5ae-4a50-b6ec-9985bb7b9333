#!/bin/bash

# 脚本用法: ./setup-issue.sh <ISSUE_ID>
# 例如: ./setup-issue.sh 122

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 请提供 ISSUE_ID"
    echo "用法: $0 <ISSUE_ID>"
    echo "例如: $0 122"
    exit 1
fi

ISSUE_ID=$1

# 验证 ISSUE_ID 是数字
if ! [[ "$ISSUE_ID" =~ ^[0-9]+$ ]]; then
    echo "错误: ISSUE_ID 必须是数字"
    echo "提供的值: $ISSUE_ID"
    exit 1
fi

echo "🚀 启动 Docker Compose，ISSUE_ID=$ISSUE_ID"

# 设置环境变量并启动 docker compose
ISSUE_ID=$ISSUE_ID docker compose up -d

# 检查启动结果
if [ $? -eq 0 ]; then
    echo "✅ Docker Compose 启动成功！"
    echo "📋 容器名称: swelancer-question-$ISSUE_ID"
    echo "🔗 端口映射: 5900:5900, 5901:5901"
    echo ""
    echo "查看容器状态: docker ps"
    echo "查看日志: docker compose logs -f"
    echo "停止容器: docker compose down"

    # auto follow logs
    docker logs -f swelancer-question-$ISSUE_ID
else
    echo "❌ Docker Compose 启动失败"
    exit 1
fi
