{"price": 250.0, "title": "[HOLD for payment 2024-12-11] [$250] Expense - On creating split expense via QAB, instead of next&save button split expense shown", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.60-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\nPre:condition: \r\n1. Create a room\r\n2. Invite few members\r\n3. Create a split expense\r\n\r\nSteps:\r\n1. Launch app\r\n2. Tap fab - split expense via QAB\r\n3. Enter an amount\r\n4. Note instead \"next\" button split expense button displayed\r\n5. Tap on split expense\r\n7. Tap amount\r\n8. Note instead \"save\" button split expense button displayed\r\n\r\n## Expected Result:\r\nOn creating split expense via QAB, instead of next&save button, split expense button must not be shown.\r\n\r\n## Actual Result:\r\nOn creating split expense via QAB, instead of next&save button, split expense button is shown.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/37da6c00-453d-4661-81c7-6465274a5ebb\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858387880530531401</li>\n        <li>Upwork Job ID: 1858387880530531401</li>\n        <li>Last Price Increase: 2024-11-25</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @adelekennedy</details>", "_issue_id": "52386"}