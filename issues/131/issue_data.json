{"price": 250.0, "title": "[HOLD for payment 2024-11-28] [$250] Onboarding task - `Take me to...` link contains -1 instead of WS ID and opens not here page", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.59.0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5195696\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Go to https://staging.new.expensify.com/ and log in with a new account\r\n2. Select \"Get paid back from my employer\" and enter a name\r\n3. Click on FAB > Submit expense\r\n4. Submit a manual expense to a new user\r\n5. Log out and log in as the other user\r\n6. Navigate to the 1:1 chat with the expense submitter\r\n7. Click on Pay with Expensify > Business bank account\r\n8. Close the Connect bank account RHP\r\n9. Navigate to Concierge\r\n10. Open an onboarding task with a link to WS settings, e.g. \"Set up categories\"\r\n11. Click on the link \"Take me to...\"\r\n\r\n## Expected Result:\r\nThe link contains the Workspace ID, and user is navigated to the corresponding workspace settings\r\n\r\n## Actual Result:\r\nThe link contains -1 instead of the Workspace ID, and user is navigated to the \"Not here\" page\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n  \r\nhttps://github.com/user-attachments/assets/1604f7e4-e14a-4255-a64b-e79f820da8a3\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856146440240693847</li>\n        <li>Upwork Job ID: 1856146440240693847</li>\n        <li>Last Price Increase: 2024-11-12</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkzie2 | Contributor | 104903064</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @trjExpensify</details>", "_issue_id": "52236"}