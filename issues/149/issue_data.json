{"price": 125.0, "title": "[HOLD for payment 2024-12-25] [$125] Copilot - Selection checkmark is displayed an access level option that is not verified yet", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.57-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**  N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go Settings > Security > Add copilot\r\n2. Select a user > Select 'Limited' for access level > Add copilot > Verify code\r\n3. Click on the added copilot > Change access level > Select 'Full'\r\n4. Click on RHP back button > Select 'Full' access again\r\n5. Click on the added copilot again\r\n\r\n## Expected Result:\r\nOn Step 4, checkmark should be displayed on 'Limited' option, and user should be navigated to magic code page on clicking 'Full' option since the access level is Limited as we see it in step 5\r\n\r\n## Actual Result:\r\nOn step 4, checkmark is displayed on 'Full' option, and RHP is closed on clicking 'Full' option\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/60d592e2-5765-4016-a1cd-385744808937\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856451982020785346</li>\n        <li>Upwork Job ID: 1856451982020785346</li>\n        <li>Last Price Increase: 2024-12-05</li>\n<li>Automatic offers: </li>\n<ul>\n<li>nyomanjyotisa | Contributor | 105213634</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @alitoshmatov</details>", "_issue_id": "51970"}