{"price": 125.0, "title": "[C+ Checklist Needs Completion] [$125] mWeb - Chat - Part of the #Admins welcome message is still in english when changing language", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.54-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5131860&group_by=cases:section_id&group_order=asc&group_id=229070\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Open the staging.new.expensify.com website\r\n2. Tap on \"Settings\" on the bottom of the screen\r\n3. Tap on \"Preferences\"\r\n4. Tap on \"Language\" and select \"Spanish\"\r\n5. Tap on the arrow on the top left corner\r\n6. Tap on \"Inbox\" and enter the \"#Admins\" chat\r\n7. Verify that the welcome message is fully displayed in spanish now\r\n\r\n## Expected Result:\r\nThe welcome message in \"#Admins\" chat should be displayed completely in spanish when changing language\r\n\r\n## Actual Result:\r\n\"Use it to chat about workspace setup and more\" in the \"#Admins\" chat welcome message, is still displayed in english when changing language\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/fa6c2a32-400d-4e02-abb3-3f3c8e8ba0e9\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021851377431726711266</li>\n        <li>Upwork Job ID: 1851377431726711266</li>\n        <li>Last Price Increase: 2024-10-29</li>\n<li>Automatic offers: </li>\n<ul>\n<li>Krishna2323 | Contributor | 104762311</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @greg-schroeder</details>", "_issue_id": "51577"}