{"price": 125.0, "title": "[HOLD for payment 2025-01-07] [$125] Add placeholder thumbnail to expenses with no receipt", "issue_repo_steps": "## Problem\r\nExpenses that don’t have an attached receipt (or eReceipt/map preview) look much different than expenses that do. This can lead to confusion especially for report previews that have a mix of expenses with and without a receipt image.\r\n\r\n## Solution\r\nComing from [this thread](https://expensify.slack.com/archives/C03U7DCU4/p1731079175338499?thread_ts=1730824248.169479&cid=C03U7DCU4), let’s add a placeholder thumbnail to expense previews that don't have any type of receipt so they more closely match our other expense previews (receipt image, eReceipt, and map preview). We can reuse styles from other preview placeholders and create a nice, consistent preview for all expenses.\r\n\r\nAdditionally, let’s update the “add receipt placeholder” on the expense view to more closely match the thumbnail presented on the preview.\r\n\r\n![image](https://github.com/user-attachments/assets/816caac7-93b3-4c0f-bcfd-376cab2ab500)\r\n\r\n![image](https://github.com/user-attachments/assets/8c3e3521-9b1b-46e2-9501-4afea905e23e)\r\n\r\n[Figma file](https://www.figma.com/design/WlrZyRCTQ0LFoKcjSmtTsd/General-UI-Patterns?node-id=4945-66207&node-type=section&t=yKW9bcMAQX2hFj1u-11)\r\n\r\ncc @Expensify/design \n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021857479879812113121</li>\n        <li>Upwork Job ID: 1857479879812113121</li>\n        <li>Last Price Increase: 2024-12-12</li>\n<li>Automatic offers: </li>\n<ul>\n<li>brunovjk | Reviewer | 104992771</li>\n<li>gijoe0295 | Contributor | 104992773</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @garrettmknight</details>", "_issue_id": "52638"}