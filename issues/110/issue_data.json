{"price": 250.0, "title": "[Payment 2024-12-20] [$250] Search - App crashes when clicking on search icon while app is loading", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.63-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go offline.\r\n3. Trigger app loading state by going to Troubleshoot > Clear cache and restart > Reset and refresh.\r\n4. Click on search icon on the top right.\r\n\r\n## Expected Result:\r\nApp will not crash.\r\n\r\n## Actual Result:\r\nApp crashes when clicking on search icon while app is loading.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n[1511_1.txt](https://github.com/user-attachments/files/17778691/1511_1.txt)\r\n\r\nhttps://github.com/user-attachments/assets/69bf093f-66f5-45ee-a38d-3a795064dc0a\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858741837244395820</li>\n        <li>Upwork Job ID: 1858741837244395820</li>\n        <li>Last Price Increase: 2024-11-19</li>\n<li>Automatic offers: </li>\n<ul>\n<li>nkdengineer | Contributor | 105177573</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @strepanier03</details>", "_issue_id": "52633"}