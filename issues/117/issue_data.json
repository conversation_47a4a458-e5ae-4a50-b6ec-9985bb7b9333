{"price": 250.0, "title": "[HOLD for payment 2025-01-13] [$250] Web - Thread - Switching to Search and back after clicking \"Join thread\" opens the report", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.61-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. (User A) Send any message to 1:1 conversation for the account you have access to\r\n2. (User B) go to the new message from User A and open context menu\r\n3. (User B) Click \"Join Thread\"\r\n4. Navigating to Settings/Search and go to Inbox again\r\n\r\n## Expected Result:\r\nThe \"Join Thread\" option should not navigate to the report.\r\n\r\n## Actual Result:\r\n\"Join thread\" option navigates to the report page after navigating to other bottom navigations like Search or Setting and returning back to inbox.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/55c0e212-9e0c-459e-8fe4-c83ee1eb9cec\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\r\n<details><summary>Upwork Automation - Do Not Edit</summary>\r\n    <ul>\r\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858763130278701100</li>\r\n        <li>Upwork Job ID: 1858763130278701100</li>\r\n        <li>Last Price Increase: 2024-12-03</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkzie2 | Contributor | 105221093</li>\n</ul></li>\r\n    </ul>\r\n</details>\r\n\r\n<details><summary>Issue Owner</summary>Current Issue Owner: @stephanieelliott</details>", "_issue_id": "52472"}