{"price": 250.0, "title": "[HOLD for payment 2024-12-20] [HOLD for payment 2024-12-11] [$250] Move onboarding tasks into the #admins room for admins", "issue_repo_steps": "### Problem \r\n\r\nBy placing onboarding steps in a Concierge DM, it limits the assigned onboarding specialist's ability to collaborate with the customer directly. \r\n\r\n\r\n### Solution\r\n\r\nWhen a user selects `Manage my team's expenses` as their onboarding intent, let's update the assigned setup specialists' message to include onboarding Tasks, and post them in the #admins room. See below:\r\n\r\n![image](https://github.com/user-attachments/assets/00f411aa-e3ee-4456-a9c7-957da061de18)\r\n\r\n1) Since the assigned onboarding specialist's message will get sent first, we'll provide onboarding steps in that same message so it doesn't seem disjointed. \r\n2) Let's update the assigned onboarding specialist's message to:\r\n\r\n> Hey there :wave: I'm your dedicated setup specialist. I look forward to helping you explore and configure Expensify. You can chat with me here anytime if you have any questions, or **book a call** with me directly at your convenience. I've shared some onboarding steps to help you get started below:\r\n> \r\n> - [ ] task 1\r\n> - [ ] task 2\r\n> - [ ] ....\r\n\r\n3) Let's keep the same GIF from the Concierge message and use it in the #admins post \r\n4) Let's move the `Start your free trial` tooltip over to the #admins row in LHN \r\n5) Let's keep the welcome message from Concierge, but not have it be pinned in LHN\r\n\r\n## Issue Checklist\r\n- [x] https://github.com/Expensify/Expensify/issues/442581\r\n- [x] https://github.com/Expensify/App/issues/52219\r\n- [x] https://github.com/Expensify/App/issues/52220\r\n\r\n<details><summary>Upwork Automation - Do Not Edit</summary>\r\n    <ul>\r\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021851283917795612465</li>\r\n        <li>Upwork Job ID: 1851283917795612465</li>\r\n        <li>Last Price Increase: 2024-11-05</li>\r\n<li>Automatic offers: </li>\r\n<ul>\r\n<li>c3024 | Contributor | 104646441</li>\r\n</ul></li>\r\n    </ul>\r\n</details>\r\n\r\n<details><summary>Issue Owner</summary>Current Issue Owner: @OfstadC</details>", "_issue_id": "51443"}