{"price": 250.0, "title": "[HOLD for payment 2025-01-18] [$250] Category receipt required amount is displaying the wrong default value. ", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.81-3\r\n**Reproducible in staging?:** Y \r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @VictoriaExpensify \r\n**Slack conversation** (hyperlinked to channel name): [#expense](https://expensify.slack.com/archives/C06ML6X0W9L/p1736209423948099)\r\n\r\n## Action Performed:\r\n1. Go to new.expensify.com\r\n2. Create a workspace \r\n3. Go to More features > Enable Rules > Upgrade\r\n4. Go to Rules \r\n5. Observe the receipt required amount is set to `£25.00`\r\n6. Go to Categories\r\n7. Click on a category value in the table to open the settings in the RHP\r\n8. Observe the \"require receipts over\" default is set to `£2000.00`\r\n9. Create and submit a manual expense on the workspace for £29.00 without a receipt\r\n10. Observe the violation for receipts being required for expenses over £25.00\r\n\r\n## Expected Result:\r\nThe default in step 8 should be displayed as £25.00 to match the receipt required amount set on the workspace. \r\n\r\n## Actual Result:\r\nThe default in step 8 is displayed as £2000.00. \r\n\r\nNote: It seems like we're displaying the max expense amount value instead, perhaps [here](https://github.com/Expensify/App/blob/3ddaee25202246dd0f6f5a699b6326072ce0a173/src/pages/workspace/categories/CategoryRequireReceiptsOverPage.tsx#L54).\r\n\r\n## Workaround:\r\nYes, the limit is correctly set to the workspace default on the backend, it's a display issue showing it as the wrong value in the category settings page. \r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n<img width=\"1511\" alt=\"image\" src=\"https://github.com/user-attachments/assets/5342dac1-725e-43a5-9203-8f52b5fba4a8\" />\r\n\r\n<img width=\"1511\" alt=\"image\" src=\"https://github.com/user-attachments/assets/c0b87c79-dc62-4d7a-a4bc-aad003c10926\" />\r\n\r\n<img width=\"1007\" alt=\"image\" src=\"https://github.com/user-attachments/assets/1db0e1a0-90ac-4162-98af-b59d39d0a307\" />\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876762664887061908</li>\n        <li>Upwork Job ID: 1876762664887061908</li>\n        <li>Last Price Increase: 2025-01-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>shubham1206agra | Reviewer | 105609818</li>\n<li>truph01 | Contributor | 105609822</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @laurenreidexpensify</details>", "_issue_id": "54916"}