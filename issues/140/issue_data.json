{"price": 250.0, "title": "[HOLD for payment 2024-11-26] [$250] The delete/backspace key on our Big Number Pad is wrongly designed", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @dannymcclain \r\n**Slack conversation** (hyperlinked to channel name): [ts_external_expensify_bugs](https://expensify-ts.slack.com/archives/C049HHMV9SM/p1730908394639119)\r\n\r\n## Action Performed:\r\n1. Launch the app\r\n2. Tap the green FAB > Submit expense\r\n3. Observe the delete/back space key\r\n## Expected Result:\r\nThe delete/backspace icon designed as  `left-caret` icon.\r\n## Actual Result:\r\nThe delete/backspace icon designed as `text` icon\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n![image (4)](https://github.com/user-attachments/assets/3d60b8dd-3bc7-4227-b16a-94b122312219)\r\n\r\n<img src=\"https://github.com/user-attachments/assets/edb8d714-f6cb-4ada-a4b2-331c7e8a9c9b\" width=\"40%\" height=\"40%\">\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021855590465266571194</li>\n        <li>Upwork Job ID: 1855590465266571194</li>\n        <li>Last Price Increase: 2024-11-10</li>\n<li>Automatic offers: </li>\n<ul>\n<li>DylanDylann | Reviewer | 104862210</li>\n<li>mkzie2 | Contributor | 104862212</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @muttmuure</details>", "_issue_id": "52156"}