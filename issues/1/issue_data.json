{"price": 250.0, "title": "[HOLD for payment 2025-02-06] [$250] Expense - Hold option is missing in the context menu", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\n___\n\n**Version Number:** 9.0.90-0\n**Reproducible in staging?:** Yes\n**Reproducible in production?:** No\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** Exp\n**Email or phone of affected tester (no customers):** <EMAIL>\n**Issue reported by:** Applause Internal Team\n**Device used:** Mac 15.0 / Chrome\n**App Component:** Money Requests\n\n## Action Performed:\n1. Go to staging.new.expensify.com\n2. Go to workspace chat.\n3. Submit two expenses to the workspace chat.\n4. Go to expense report.\n5. Click on the expense preview.\n6. Go back to the expense report.\n7. Right click on the expense preview.\n\n## Expected Result:\nThere should be Hold option in the context menu (production behavior).\n\n## Actual Result:\nHold option is missing in the context menu.\n\n## Workaround:\nUnknown\n\n## Platforms:\n- [x] Android: Standalone\n- [x] Android: HybridApp\n- [x] Android: mWeb Chrome\n- [x] iOS: Standalone\n- [x] iOS: HybridApp\n- [x] iOS: mWeb Safari\n- [x] MacOS: Chrome / Safari\n- [x] MacOS: Desktop\n\n## Screenshots/Videos\n\nhttps://github.com/user-attachments/assets/b821e159-ea71-4d2d-836c-ebd605fb42c0\n\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021884172494858147022</li>\n        <li>Upwork Job ID: 1884172494858147022</li>\n        <li>Last Price Increase: 2025-01-28</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @mallenexpensify</details>", "_issue_id": "55827"}