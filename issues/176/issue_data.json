{"price": 125.0, "title": "[HOLD for payment 2024-11-13] [$125] Update `Start a free trial` on the Concierge DM header to be clickable", "issue_repo_steps": "Problem: Users keep clicking `Start a free trial` in the Concierge DM header, thinking it will help them start their free trial, but it doesn’t take them anywhere, so they are confused about how to start the trial.\r\n\r\nSolution: Make the notice in the Concierge DM header a clickable button that takes you to Settings > Subscriptions (https://new.expensify.com/settings/subscription) \r\n\r\nPlease note: We have the \"Start a free trial\" notice in the LHN on the Concierge DM and in Settings Subscriptions. We do not need to make those clickable; we're only addressing the one in the Concierge DM header. \r\n\r\n<img width=\"1415\" alt=\"2024-10-21_11-42-44\" src=\"https://github.com/user-attachments/assets/ca96b2e5-0051-4d42-afaf-66bb4cf36ca3\">\r\n\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848465720168177047</li>\n        <li>Upwork Job ID: 1848465720168177047</li>\n        <li>Last Price Increase: 2024-10-21</li>\n<li>Automatic offers: </li>\n<ul>\n<li>brunovjk | Reviewer | 104550274</li>\n<li>FitseTLT | Contributor | 104550276</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @mallenexpensify</details>", "_issue_id": "51197"}