{"price": 250.0, "title": "[HOLD for payment 2024-11-29] [$250] Group chat - New member is displayed with offline profile avatar", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.63-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5229966&group_by=cases:section_id&group_id=296760&group_order=asc\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Create group conversation with few members\r\n2. Click on header> Members> Invite new member\r\n3. Enter email address and take a look at user's avatar\r\n\r\n## Expected Result:\r\nUser should have default or custom profile avatar\r\n\r\n## Actual Result:\r\nUser is displayed with offline profile avatar when invite it in existing group chat\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/960c7f37-ca6f-4151-9804-eb5ce6428d4c\r\n\r\n  </details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858590656610669336</li>\n        <li>Upwork Job ID: 1858590656610669336</li>\n        <li>Last Price Increase: 2024-11-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>brunovjk | Reviewer | 104980522</li>\n<li>gijoe0295 | Contributor | 104980525</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @garrettmknight</details>", "_issue_id": "52639"}