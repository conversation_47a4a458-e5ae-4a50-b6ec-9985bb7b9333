{"price": 250.0, "title": "[HOLD for payment 2024-12-09] [$250] Members- Refresh while on the WS invite message page empty show invite message again", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.55-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n**Issue found when executing PR** https://github.com/Expensify/App/pull/51228\r\n## Action Performed:\r\n1. Open the WS invite page\r\n2. Select any user and go next\r\n3. Empty the message\r\n4. Refresh the page\r\n\r\n## Expected Result:\r\nRefresh while on the WS invite message page with empty message should show message place empty as it was before refreshed\r\n\r\n## Actual Result:\r\nRefresh while on the WS invite message page empty show invite message all over again\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/32c9879d-7df8-4629-9808-bfb42af99d98\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021853393721581519961</li>\n        <li>Upwork Job ID: 1853393721581519961</li>\n        <li>Last Price Increase: 2024-11-25</li>\n<li>Automatic offers: </li>\n<ul>\n<li>allgandalf | Reviewer | 105068622</li>\n<li>wildan-m | Contributor | 105068623</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @allgandalf</details>", "_issue_id": "51655"}