{"price": 250.0, "title": "[HOLD for payment 2024-11-29] [HOLD for payment 2024-11-20] [HOLD for payment 2024-11-13] [$250] Add a step that collects the magic code when adding a VBBA", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.51-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Applicable on all platforms\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @trjExpensify \r\n**Slack conversation:** https://expensify.slack.com/archives/C07HPDRELLD/p1729255848407089?thread_ts=**********.964519&cid=C07HPDRELLD\r\n\r\n## Action Performed:\r\n1. Go to expensify.com > sign-up > choose \"1-9\" to be redirected to NewDot \r\n2. Complete the onboarding modal steps to have a workspace created.\r\n3. Go to Settings > Workspaces > Click into the workspace created\r\n4. Go to More features > Enable workflows \r\n5. Go to Workflows > Make or track payments > Connect bank account\r\n\r\n## Expected Result:\r\n**This is a feature request.** \r\n\r\n1. Neither the \"Connect online with Plaid\" or \"Connect manually\" option rows are greyed out \r\n2. There isn't a \"Hold up! We need you to...\" error message.\r\n3. When clicking either of the option rows in 1 above, we send a magic code email to the user, and show this validate your account page to collect it:\r\n\r\n<img width=\"219\" alt=\"image\" src=\"https://github.com/user-attachments/assets/545c0290-ff66-4e17-aa8f-fea8f72e2ec2\">\r\n\r\n## Actual Result:\r\n1. Both option rows are greyed out \r\n2. There's an error message on the page which we've since deprecated elsewhere in favour of a better experience to fire off and collect a magic code. \r\n\r\n<img width=\"376\" alt=\"image\" src=\"https://github.com/user-attachments/assets/bd7eec8f-9cd7-4066-8c83-00fb694b879f\">\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nIn-line above. \r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\r\n\r\nCC: @shawnborton @mountiny \n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848301297798537201</li>\n        <li>Upwork Job ID: 1848301297798537201</li>\n        <li>Last Price Increase: 2024-10-21</li>\n<li>Automatic offers: </li>\n<ul>\n<li>shahinyan11 | Contributor | 104639697</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @garrettmknight</details>", "_issue_id": "51166"}