{"price": 250.0, "title": "[HOLD for payment 2024-11-11] [$250] Search - \"Saved\" text in search has a slight delay after deleting the term", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.50-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Access staging.new.expensify.com\r\n2. Sign into a valid account\r\n3. Tap \"Search\" at the bottom > Apply a Filter > Save the filter\r\n4. Delete the Saved filter by click on the 3 dot menu > Delete\r\n5. Observe the \"Saved\" text\r\n\r\n## Expected Result:\r\nUser expects that when the filter is deleted, the text disappears immediately\r\n\r\n## Actual Result:\r\nThe \"Saved\" text still appears for a couple of seconds after the filter was deleted\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/9b5f448b-2f25-4d23-a664-7c78976e9704\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021849069011992742790</li>\n        <li>Upwork Job ID: 1849069011992742790</li>\n        <li>Last Price Increase: 2024-10-23</li>\n<li>Automatic offers: </li>\n<ul>\n<li>dukenv0307 | Reviewer | 104578163</li>\n<li>daledah | Contributor | 104578164</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @sonialiap</details>", "_issue_id": "51028"}