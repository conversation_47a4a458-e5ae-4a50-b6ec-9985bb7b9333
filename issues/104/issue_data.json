{"price": 250.0, "title": "[HOLD for payment 2025-01-28] [$250] Update \"Hold expense\" modal copy and icons", "issue_repo_steps": "## Problem\r\nComing from this [long convo](https://expensify.slack.com/archives/C06ML6X0W9L/p1731326524011019), the current \"Hold expense\" modal copy isn't very informative in terms of how held expenses interact with ongoing report approvals/payments. This leads to user confusion and uncertainty as noted in the thread linked above.\r\n\r\n## Solution\r\nUpdate the \"Hold expense\" modal copy (and icons) to more clearly explain how held expenses interact with ongoing report approvals/payments. \r\n\r\n## Mocks\r\n![image](https://github.com/user-attachments/assets/6b14a6f3-6501-4b7f-8cfa-006d00d3a3d0)\r\n\r\n## Copy\r\n\r\n> **This request is on \"hold\"**\r\n> Hold is like hitting “pause” on an expense to ask for more details before approval or payment.\r\n> - Held expenses are left behind even if you approve an entire report.\r\n> - Unhold expenses when you’re ready to approve or pay.\r\n\r\n> **Esta solicitud está \"retenida\"**\r\n> Retener es como \"pausar\" un gasto para solicitar más detalles antes de aprobarlo o pagarlo.\r\n> - Si apruebas un informe, los gastos retenidos se quedan fuera de esa aprobación.\r\n> - Desbloquea los gastos cuando estés listo para aprobarlos o pagarlos.\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021859310506550140217</li>\n        <li>Upwork Job ID: 1859310506550140217</li>\n        <li>Last Price Increase: 2024-11-20</li>\n<li>Automatic offers: </li>\n<ul>\n<li>gijoe0295 | Contributor | 105094034</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @JmillsExpensify</details>", "_issue_id": "52655"}