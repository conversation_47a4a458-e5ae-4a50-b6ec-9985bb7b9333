{"price": 250.0, "title": "[HOLD for payment 2025-01-30] [$250] EReceipt thumbnail is being cut off at the bottom.", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.80-6\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @jayeshmangwani \r\n**Slack conversation** (hyperlinked to channel name): [expensify_bugs](https://expensify.slack.com/archives/C049HHMV9SM/p1735852826393029)\r\n\r\n## Action Performed:\r\n1. Press the FAB button.\r\n2. Select Create Expense.\r\n3. Tap Scan and choose a file.\r\n4. Upload an invalid image file (example provided below).\r\n5. Select any recipient.\r\n6. Observe the default thumbnail for the receipt.\r\n## Expected Result:\r\nEReceipt thumbnail should be fully visible with rounded corners.\r\n## Actual Result:\r\nEReceipt thumbnail is partially cut off at the bottom.\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n\r\nhttps://github.com/user-attachments/assets/29031dbe-5da6-4e19-acdd-376f16f6ea37\r\n\r\n![Screenshot 2025-01-03 at 2 51 46 AM](https://github.com/user-attachments/assets/e620abb4-9412-44f4-90f9-9176a802b18c)\r\n\r\n![Full Screen #49](https://github.com/user-attachments/assets/757b6fd8-46e4-4f77-a223-4c588f7eed56)\r\n\r\n[Example image for which Issue can be reproduced](https://expensify.enterprise.slack.com/files/U023502UBS7/F08746TGXRQ/file_example_tiff_1mb.tiff) \r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021879191008215577946</li>\n        <li>Upwork Job ID: 1879191008215577946</li>\n        <li>Last Price Increase: 2025-01-14</li>\n<li>Automatic offers: </li>\n<ul>\n<li>paultsimura | Reviewer | 105740538</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @slafortune</details>", "_issue_id": "54772"}