from playwright.sync_api import sync_playwright
import time
import imaplib
import email
import re
import random
import pytest

# Constants for URLs and credentials
NEWDOT_URL = "https://dev.new.expensify.com:8082/"
OLDDOT_URL = "http://localhost:9000/"
<<<<<<< HEAD:issues/100/user_tool.py
EMAIL = "<EMAIL>"
PASSWORD = ""
=======
EMAIL = "<EMAIL>"
PASSWORD = "dzcr knad otxp bsuz"
>>>>>>> main:issues/186/test.py


class IMAPOTPExtractor:
    def __init__(self, email_address, password):
        if not self._validate_email(email_address):
            raise ValueError("Invalid email address format.")
        self.original_email = email_address
        self.password = password

        random_suffix = random.randint(1000, 9999)
        current_timestamp = int(time.time())
        random_suffix = f"{current_timestamp}{random_suffix}"

    def _validate_email(self, email_address):
        return bool(re.match(r"[^@]+@[^@]+\.[^@]+", email_address))

    def set_new_email(self, email):
        self.new_email = email

    def check_for_otp(self):
        try:
            mail = imaplib.IMAP4_SSL("imap.gmail.com")
            mail.login(self.original_email, self.password)
            mail.select("inbox")

            status, messages = mail.search(None, "UNSEEN")
            if status != "OK":
                return None

            email_ids = messages[0].split()
            email_ids = email_ids[::-1]

            for email_id in email_ids:
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != "OK":
                    continue

                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])

                        if self.new_email in msg["To"]:
                            otp_code = self._extract_otp_from_email(msg)
                            if otp_code:
                                mail.store(email_id, '+FLAGS', '\\Deleted')
                                mail.expunge()
                                mail.close()
                                mail.logout()
                                return otp_code

            mail.close()
            mail.logout()
            return None

        except imaplib.IMAP4.error:
            return None

    def _extract_otp_from_email(self, msg):
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    email_body = part.get_payload(decode=True).decode()
                    otp_code = self._extract_otp(email_body)
                    if otp_code:
                        return otp_code
        else:
            email_body = msg.get_payload(decode=True).decode()
            otp_code = self._extract_otp(email_body)
            if otp_code:
                return otp_code
        return None

    def _extract_otp(self, text):
        match = re.search(r"\b\d{6}\b", text)
        return match.group(0) if match else None


# Initialize the OTP extractor
gmail_account_helper = IMAPOTPExtractor(EMAIL, PASSWORD)


# Login helper function
def new_dot_login(page, email):
    page.goto(NEWDOT_URL)
    page.locator('input[type="email"]').fill(email)
    page.wait_for_timeout(2000)
    page.get_by_role("button", name="Continue").nth(0).click()
    # page.wait_for_timeout(10000)
    # gmail_account_helper.set_new_email(email)
    # otp = gmail_account_helper.check_for_otp()
    otp = '123456'
    page.locator('input[data-testid="validateCode"]').fill(otp)
    try:
        page.get_by_role("button", name="Sign In").click()
    except:
        pass

@pytest.fixture(scope="module")
def setup_browser():
    """Initialize browser and page context for all tests within a single session."""
    with sync_playwright() as p:
        browser = p.chromium.launch(
            headless=False,
            args=[
                "--ignore-certificate-errors",
                "--disable-web-security",
                "--disable-features=IsolateOrigins,site-per-process"
            ],
            slow_mo=1000
        )
        context = browser.new_context(ignore_https_errors=True)
        page = context.new_page()
        yield page
        browser.close()


@pytest.fixture(scope="module")
def login_to_new_dot(setup_browser):
    """Log into the Expensify application with the provided email."""
    page = setup_browser
    new_dot_login(page, EMAIL)
    return page

def create_room(page):
    room_name = f"testroom-1739573689-abc"
    page.wait_for_timeout(2000)
    page.get_by_label("Start chat (Floating action)").click()
    page.get_by_label("Start chat", exact=True).click()
    page.get_by_label("Room", exact=True).click()
    page.get_by_placeholder("social").fill(room_name)
    page.wait_for_timeout(2000)
    page.get_by_role("button", name="Create room").click()
    return room_name

def set_room_description(page, room_name):
    page.locator(f'//button[@aria-label="#{room_name}"]').last.click()
    room_setting_page = page.locator('//div[@data-testid="ReportDetailsPage"]')
    room_setting_page.locator('//div[contains(text(), "Room description")]').click()
    page.locator('//div[@aria-label="Room description"]').last.fill('@test')
    page.wait_for_timeout(2000)
    page.locator('//div[contains(text(), "Save")]').last.click()
    room_setting_page.locator('//button[@aria-label="Back"]').last.click()


def verify_description_text(page):
    room_description_text = page.locator('//div[@aria-label="Room description"]').last.text_content()

    assert room_description_text == "@test", "Room description text mismatch"

def test_search_icon_is_present_on_company_address(login_to_new_dot):
    """Verify that the payment complete message is displayed in Spanish when language preference is set to Spanish."""

    page = login_to_new_dot
    page.wait_for_timeout(5000)
    room_name = create_room(page)
    set_room_description(page, room_name)
    verify_description_text(page)
