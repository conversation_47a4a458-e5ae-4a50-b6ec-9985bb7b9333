{"price": 100.0, "title": "[HOLD for payment 2024-12-11] [$100] mWeb - Profile - Device back button leads to profile and not to calendar when in years tab", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.60-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Open the staging.new.expensify.com website.\r\n2. Tap on \"Settings\" on the bottom of the screen.\r\n3. Tap on \"Profile\"\r\n4. Scroll down to \"Date of Birth\" and open the tab.\r\n5. Tap on the year, to open the years list.\r\n6. Use device´s back button to return to calendar.\r\n7. Verify you land on the calendar tab again.\r\n\r\n## Expected Result:\r\nWhen opening the years list when setting date of birth and using device´s back button, the user should be redirected to the calendar tab again.\r\n\r\n## Actual Result:\r\nWhen opening the years list when setting date of birth and using device´s back button, the user lands on the profile tab again and not on the calendar.\r\n\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/61ac56c2-bead-48e4-a323-acfddffe6a2e\r\n\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858415095578104254</li>\n        <li>Upwork Job ID: 1858415095578104254</li>\n        <li>Last Price Increase: 2024-11-18</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @VictoriaExpensify</details>", "_issue_id": "52383"}