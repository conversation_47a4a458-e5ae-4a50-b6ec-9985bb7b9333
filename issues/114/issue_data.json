{"price": 250.0, "title": "[HOLD for payment 2024-12-07] [$250] No RBR on Subscription row in the LHN in settings if the free trial has expired", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.62-3\r\n**Reproducible in staging?:** y\r\n**Reproducible in production?:** y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @allgandalf \r\n**Slack conversation** (hyperlinked to channel name): [Expensify bugs](https://applause-ts.slack.com/archives/C049HHMV9SM/p1731578046121969)\r\n\r\n## Action Performed:\r\n\r\n1. Open an account who free trial has expired\r\n2. Go to settings > Subscription\r\n\r\n\r\n## Expected Result:\r\nRBR should also be on the Subscription row in the LHN\r\n## Actual Result:\r\nObserve that there is a RBR on the Page but not on the settings option\r\n## Workaround:\r\nCan the user still use Expensify without this being fixed? Have you informed them of the workaround?\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n![Screenshot 2024-11-14 at 3 24 16 PM (1)](https://github.com/user-attachments/assets/9b9b9ad0-a8bc-45ce-a8f4-91d29799a359)\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858434959504168009</li>\n        <li>Upwork Job ID: 1858434959504168009</li>\n        <li>Last Price Increase: 2024-11-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>daledah | Contributor | 104948042</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @zanyrenney</details>", "_issue_id": "52598"}