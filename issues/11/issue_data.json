{"price": 250.0, "title": "[HOLD for payment 2025-01-28] [$250] Per diem - Labels show quantities only in the plural", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.81-3\r\n**Reproducible in staging?:** Y \r\n**Reproducible in production?:** Unable to check\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://github.com/Expensify/App/pull/54252\r\n**Issue reported by:** Applause - Internal Team\r\n**Component:** Money Requests\r\n\r\n## Action Performed:\r\nWorkspace has Per diem enabled and Per diem rates loaded\r\n1. Go to staging.new.expensify.com\r\n2. Go to FAB > Create expense > Per diem tab\r\n3. Select any country\r\n4. Select start and end dates\r\n5. Select any Subrate\r\n6. Enter any Quantity\r\n7. Click on Save\r\n\r\n## Expected Result:\r\nLabels show quantities in plural when the quantity of days or hours is greater than 1 and in singular when the quantity is equal to 1\r\n\r\n## Actual Result:\r\nLabels show quantities only in the plural. The behavior is similar in Spanish\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/d5c12c8f-326d-4dfc-a04b-8c60a2850c27\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021877030743068052884</li>\n        <li>Upwork Job ID: 1877030743068052884</li>\n        <li>Last Price Increase: 2025-01-08</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>", "_issue_id": "54905"}