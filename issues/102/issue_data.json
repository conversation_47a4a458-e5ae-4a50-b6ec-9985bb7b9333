{"price": 250.0, "title": "[HOLD for payment 2024-12-07] [$250] Tag rules - Approver field in tag editor shows user email instead of user name", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.63-3\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to workspace settings > Workflows.\r\n3. Click on the existing approval workflow under Add approvals.\r\n4. Note that Approver field shows user name.\r\n5. Go to workspace settings > Tags.\r\n6. Click on any tag.\r\n7. Click Approver.\r\n8. Select a user with custom name.\r\n\r\n## Expected Result:\r\nApprover field in tag editor will show user name.\r\n\r\n## Actual Result:\r\nApprover field in tag editor shows user email instead of user name.\r\n\r\nIn Category editor, Approver field shows user name.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/36707c93-fe19-4452-a0ec-e8eec0b54c9d\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858313853614949592</li>\n        <li>Upwork Job ID: 1858313853614949592</li>\n        <li>Last Price Increase: 2024-11-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>etCoderDysto | Contributor | 104985198</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @kadiealexander</details>", "_issue_id": "52678"}