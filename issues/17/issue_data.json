{"price": 250.0, "title": "[Due for payment 2025-01-18] [$250] Mark the self tour as completed when user returns from Navattic", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:**\r\n**Reproducible in staging?:**\r\n**Reproducible in production?:**\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:**\r\n**Slack conversation** (hyperlinked to channel name):\r\n\r\n## Action Performed:\r\nBreak down in numbered steps\r\n\r\nWhen user completes the Self Tour in Navattic, then the url will newly include `?navattic=completed` query param. \r\n\r\n## Expected Result:\r\nDescribe what you think should've happened\r\n\r\nWhen the user returns to Expensify to the ConciergePage with this param, make sure to call the SelfTourViewed API\r\n\r\n## Actual Result:\r\nDescribe what actually happened\r\n\r\nNothing happens now\r\n\r\n## Workaround:\r\nCan the user still use Expensify without this being fixed? Have you informed them of the workaround?\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876310402323638158</li>\n        <li>Upwork Job ID: 1876310402323638158</li>\n        <li>Last Price Increase: 2025-01-06</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @RachCHopkins</details>", "_issue_id": "54815"}