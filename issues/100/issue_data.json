{"price": 250.0, "title": "[HOLD for payment 2024-12-30] [$250] IOU - Workspace name is duplicate on report header for submitter", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.64-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5238580&group_by=cases:section_id&group_order=asc&group_id=309128\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\nPrecondition:\r\n\r\n1. <PERSON><PERSON> as the owner of the workspace\r\n2. Create a workspace\r\n3. Invite the approver and employee\r\n4. Navigate to more features\r\n5. Enable \"workflows\"\r\n6. On the \"Workflow\" editor - enable \"Add Approvals\"\r\n7. Set the Approver account as the Approver\r\n\r\nSteps:\r\n\r\n1. As employee submit 2 expenses in workspace chat\r\n2. As approver open the report with 2 expenses\r\n3. check the header to make sure it shows the Report ID, workspace and submitter.\r\n\r\n<img width=\"428\" alt=\"image\" src=\"https://github.com/user-attachments/assets/051e6e60-7215-4ce4-8b73-b70799f91ead\">\r\n\r\n4. As the submitter, notice the report ID and workspace name are shown twice. \r\n<img width=\"457\" alt=\"image\" src=\"https://github.com/user-attachments/assets/55b125dc-2b22-482b-8d2d-349b2a318c32\">\r\n\r\n\r\n## Expected Result:\r\nFor the submitter, the report header should not include `in %workspaceName%` as it's repetitive. The workspace chat is the workspace name for them. \r\n\r\n## Actual Result:\r\nFor the submitter, we're repeating the workspace name.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/ba6230fb-1f75-4c13-926c-9019a52a34b6\r\n\r\n\r\n\r\n  </details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021859415514696293689</li>\n        <li>Upwork Job ID: 1859415514696293689</li>\n        <li>Last Price Increase: 2024-11-21</li>\n    </ul>\n</details>\n\n\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @thesahindia</details>", "_issue_id": "52763"}