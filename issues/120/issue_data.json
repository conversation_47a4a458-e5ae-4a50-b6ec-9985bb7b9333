{"price": 250.0, "title": "[HOLD for payment 2024-12-12] [$250] Report - Use + to submit expense is displayed instead of to create expense", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.60-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Click on FAB > Start chat > select a new user\r\n2. Click + button\r\n\r\n## Expected Result:\r\nThe copy above the composer should say 'create' instead of 'submit' to reflect the change from submit expense to create expense in context menu\r\n\r\n## Actual Result:\r\nThe copy above the composer shows submit expense\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n<img width=\"1710\" alt=\"Bug6662123_1731390014799!Screenshot_2024-11-12_at_8 39 42_in_the_morning\" src=\"https://github.com/user-attachments/assets/6d0f9243-046d-405d-8296-fdf7374cf829\">\r\n\r\nhttps://github.com/user-attachments/assets/c00eb47f-f7e0-49e6-a001-b21fb3dd794b\r\n\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021857384321549867205</li>\n        <li>Upwork Job ID: 1857384321549867205</li>\n        <li>Last Price Increase: 2024-11-15</li>\n<li>Automatic offers: </li>\n<ul>\n<li>daledah | Contributor | 105073749</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @rushatgabhane</details>", "_issue_id": "52393"}