{"price": 250.0, "title": "[HOLD for payment 2024-11-29] [$250] mWeb - Inbox- When creating WS via FAB and using device back button, rest of chats disappear", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.55-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5141100&group_by=cases:section_id&group_order=asc&group_id=229067\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Open the staging.new.expensify.com website\r\n2. Sign in with new account\r\n3. Complete the onboarding process\r\n4. Tap on the FAB and select \"New Workspace\"\r\n5. Once redirected to workspace details page, use the device back button to return to inbox\r\n6. Verify that all the chats, including the new workspace chat, are displayed in inbox\r\n\r\n## Expected Result:\r\nWhen the user creates a new workspace via FAB and returns to inbox using device back button, all the chats should be visible\r\n\r\n## Actual Result:\r\nWhen creating a new workspace via FAB, and returning to inbox using device back button, the rest of the chats disappear and only the workspace chat is visible until navigating to a different category and returning\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/c79b12ea-b5fd-40eb-8713-bc8649625505\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856428632364438612</li>\n        <li>Upwork Job ID: 1856428632364438612</li>\n        <li>Last Price Increase: 2024-11-12</li>\n<li>Automatic offers: </li>\n<ul>\n<li>brunovjk | Reviewer | 104913598</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @abekkala</details>", "_issue_id": "51658"}