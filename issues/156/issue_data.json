{"price": 250.0, "title": "[HOLD for payment 2024-11-20] [$250] QBO - Back button on workspace editor returns to Accounting after changing Import settings", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.55-4\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** No, new feature\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- Workspace is connected to QBO\r\n1. Go to www.staging.chat.expensify.com\r\n2. Go to workspace settings > Accounting\r\n3. Click Import\r\n4. Click Classes > Tap Displayed as\r\n5. Select Tags or Report fields\r\n6. Click back button twice to return to Accounting page\r\n7. Click back button to return to workspace editor\r\n8. Click back button again\r\n\r\n## Expected Result:\r\nApp will return to workspace list\r\n\r\n## Actual Result:\r\nApp returns to Accounting page when tapping back button from workspace editor after changing Import settings\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/48a0967a-9abb-4aca-b68c-3df444f7b462\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021851664505290782444</li>\n        <li>Upwork Job ID: 1851664505290782444</li>\n        <li>Last Price Increase: 2024-10-30</li>\n<li>Automatic offers: </li>\n<ul>\n<li>FitseTLT | Contributor | 104739114</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @slafortune</details>", "_issue_id": "51696"}