{"price": 250.0, "title": "[HOLD for payment 2024-11-11] [$250] Approver - Existing approver settings reverts to default after upgrading workspace plan", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.49-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- Workspace is under Collect plan.\r\n- Workspace has at least two members.\r\n\r\n1. Go to staging.new.expensify.com\r\n2. Go to workspace settings > Workflows.\r\n3. Enable Add approvals.\r\n4. Click on the existing approval workflow.\r\n5. Click Approver.\r\n6. Select another member as approver and save it.\r\n7. Click Additional approver.\r\n8. Upgrade the plan.\r\n9. Click Got it, thanks.\r\n\r\n## Expected Result:\r\nThe existing approver settings in Step 6 will be preserved.\r\n\r\n## Actual Result:\r\nThe existing approver settings in Step 6 reverts to default after upgrading workspace plan.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n\r\nhttps://github.com/user-attachments/assets/0ebb8d1c-91f2-428d-b2a0-4b1cbb47ed7a\r\n\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021847030936559806669</li>\n        <li>Upwork Job ID: 1847030936559806669</li>\n        <li>Last Price Increase: 2024-10-17</li>\n<li>Automatic offers: </li>\n<ul>\n<li>ishpaul777 | Reviewer | 104559184</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @joekaufmanexpensify</details>", "_issue_id": "50924"}