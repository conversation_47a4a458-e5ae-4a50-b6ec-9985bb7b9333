{"price": 250.0, "title": "[HOLD for payment 2024-11-28] [$250] mWeb - Attachments - 1.75, 2 playback speed options are not displayed", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5184936&group_by=cases:section_id&group_order=asc&group_id=292107\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n\r\n1. On a conversation, click on the + button in the compose box\r\n2. Click on add attachment\r\n3. Upload a Video\r\n4. Click on the three dots menu -> Playback speed.\r\n\r\n## Expected Result:\r\nAll playback speed options (0.25, 0.5, 0.75, Normal, 1.25, 1.5, 1.75, 2) can be observed.\r\n\r\n## Actual Result:\r\n1.75, 2 playback speed options are not displayed\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n\r\nhttps://github.com/user-attachments/assets/8900aadc-7fb6-4976-a3f2-e8bd8c4b4234\r\n\r\n\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854881579584772026</li>\n        <li>Upwork Job ID: 1854881579584772026</li>\n        <li>Last Price Increase: 2024-11-08</li>\n<li>Automatic offers: </li>\n<ul>\n<li>akinwale | Reviewer | 104847298</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @zanyrenney</details>", "_issue_id": "52114"}