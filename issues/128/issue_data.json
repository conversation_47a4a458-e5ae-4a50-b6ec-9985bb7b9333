{"price": 250.0, "title": "[HOLD for payment 2024-12-05] [$250]  Dupe detection - RBR is not shown when creating dupe expense with a Gmail account", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.59-0 \r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue was found when executing this PR:** https://github.com/Expensify/App/pull/48958\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Login to an account.\r\n2. Create a workspace\r\n3. Open the Workspace chat\r\n4. Submit an expense in the Workspace with a category,\r\n5. Submit another expense in the Workspace with a the same amount and merchant in the above but with no category.\r\n\r\n## Expected Result:\r\nRBR(red dot) is shown in LHN and also in the report because of the dupe expense.\r\n\r\n## Actual Result:\r\nRBR(red dot) is not shown in LHN and in the report. RBR is only shown inside the expense report\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\nhttps://github.com/user-attachments/assets/6067710d-aceb-409e-b71b-b12145014331\r\n\r\n  </details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021857074546459286027</li>\n        <li>Upwork Job ID: 1857074546459286027</li>\n        <li>Last Price Increase: 2024-11-21</li>\n<li>Automatic offers: </li>\n<ul>\n<li>ahmedGaber93 | Reviewer | 105026209</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @muttmuure</details>", "_issue_id": "52243"}