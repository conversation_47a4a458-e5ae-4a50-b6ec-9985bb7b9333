{"price": 250.0, "title": "[HOLD for payment 2024-10-29] [$250] Search - No tooltip when hovering over the search icon", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.50-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N/A - new feature, doesn't exist in prod\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Open any report.\r\n3. Hover over search icon on the top right.\r\n\r\n## Expected Result:\r\nThere will be tooltip when hovering over the search icon.\r\n\r\n## Actual Result:\r\nThere is no tooltip when hovering over the search icon.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/10cc26b1-4f1e-4f2d-a152-fff2b65a798a\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021846935135638966053</li>\n        <li>Upwork Job ID: 1846935135638966053</li>\n        <li>Last Price Increase: 2024-10-17</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @joekaufmanexpensify</details>", "_issue_id": "50977"}