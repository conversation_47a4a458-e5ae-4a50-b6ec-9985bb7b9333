{"price": 250.0, "title": "[$250] Expense - Admin can submit expense to member's workspace chat via FAB", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.81-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n**Component:** Money Requests\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- User is logged in with Expensifail account\r\n1. Go to staging.new.expensify.com\r\n2. Create a workspace\r\n3. Invite a few Expensifail users (do not invite Gmail) to the workspace (do not open workspace chat with members)\r\n4. Go to FAB > Create expense > Manual\r\n5. Enter amount > Next\r\n6. Select member's workspace chat (the workspace chat name will be the workspace name because the workspace chat is not opened in Step 3)\r\n7. Submit the expense\r\n\r\n## Expected Result:\r\nIn Step 6, member's workspace chat should not appear in \"Workspace\" list when submitting expense\r\n\r\n## Actual Result:\r\nIn Step 6, member's workspace chat appears in \"Workspace\" list when submitting expense and admin can send expense to member's workspace chat\r\nThis issue happens when member's workspace chat is not opened yet\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/da94f7c9-0fb3-454d-9ebc-cb771b8d10cd\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876740693896953761</li>\n        <li>Upwork Job ID: 1876740693896953761</li>\n        <li>Last Price Increase: 2025-01-07</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @eh2077</details>", "_issue_id": "54846"}