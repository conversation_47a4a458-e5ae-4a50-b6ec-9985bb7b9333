{"price": 250.0, "title": "[HOLD for payment 2024-12-07] [$250] The up-to-edit feature doesn't work when the message contains an embedded image.", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.59-3\r\n**Reproducible in staging?:** Needs Reproduction\r\n**Reproducible in production?:** Needs Reproduction\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @quinthar \r\n**Slack conversation** (hyperlinked to channel name): [ts_external_expensify_bugs](https://expensify.slack.com/archives/C049HHMV9SM/p1731121206505619)\r\n\r\n## Action Performed:\r\n1. Navigate to any chat\r\n2. Compose any message and add a attachment\r\n3. Send the message\r\n4. Press the up arrow on the keyboard to edit\r\n## Expected Result:\r\nUser able to edit the sent message\r\n## Actual Result:\r\nUp to edit feature doesn't work\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n\r\nhttps://github.com/user-attachments/assets/c85f012b-ca8c-497b-9acf-142f1c5b9a4a\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856262688043659757</li>\n        <li>Upwork Job ID: 1856262688043659757</li>\n        <li>Last Price Increase: 2024-11-12</li>\n<li>Automatic offers: </li>\n<ul>\n<li>daledah | Contributor | 104956482</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @jliexpensify</details>", "_issue_id": "52319"}