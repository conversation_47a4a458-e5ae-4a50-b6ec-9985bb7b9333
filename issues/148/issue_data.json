{"price": 250.0, "title": "[HOLD for payment 2024-12-11] [$250] Workspace- Upgrade animation freezes when clicking on \"View your subscription\" and returning", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.57-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Log in with a new Gmail account\r\n2. Create a workspace\r\n3. Navigate to Workspace settings - More features\r\n4. Enable \"Report fields\"\r\n5. Click on \"Upgrade\"\r\n6. Click on \"View your subscription\"\r\n7. Click on the browsers \"back\" button\r\n\r\n## Expected Result:\r\nUpgrade animation should be playing or the RHP should disappear.\r\n\r\n## Actual Result:\r\nUpgrade animation freezes when clicking on \"View your subscription\" and returning to the page.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/416d356c-b204-44b2-8388-ab5c9175d29b\r\n\r\n  </details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854048484565910103</li>\n        <li>Upwork Job ID: 1854048484565910103</li>\n        <li>Last Price Increase: 2024-11-13</li>\n<li>Automatic offers: </li>\n<ul>\n<li>abzokhattab | Contributor | 104959985</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @VictoriaExpensify</details>", "_issue_id": "51982"}