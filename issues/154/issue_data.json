{"price": 250.0, "title": "[HOLD for payment 2024-11-20] [$250] Scan - Infinite loading for scan option if it is selected after manual.", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.56-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5168073&group_by=cases:section_id&group_order=asc&group_id=309127\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Click on FAB\r\n2. Click on submit expense\r\n3. Verify the manual option is selected\r\n4. Select the Scan option\r\n\r\n## Expected Result:\r\n\"Take a photo, Camera access is required to take pictures of receipts\" or a camera interface are displayed\r\n\r\n## Actual Result:\r\nInfinite loading for scan option if it is selected after manual\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/912f1fa8-c8a7-49f5-adc6-9e00578b9843\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021852097315271105119</li>\n        <li>Upwork Job ID: 1852097315271105119</li>\n        <li>Last Price Increase: 2024-10-31</li>\n<li>Automatic offers: </li>\n<ul>\n<li>QichenZhu | Contributor | 104755565</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @joekaufmanexpensify</details>", "_issue_id": "51821"}