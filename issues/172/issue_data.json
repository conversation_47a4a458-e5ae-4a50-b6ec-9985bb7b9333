{"price": 250.0, "title": "[HOLD for payment 2024-11-18] [$250] Workspace - WS chat is created for invalid phone number", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.52-5\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:**Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Login to an account\r\n2. create a WS\r\n3. Invite a user with invalid Phone number(Invalid Phone: ******-791-8378)\r\nNotice error is shown and can't invite user.\r\n4. Go to inbox\r\n\r\n## Expected Result:\r\nWS chat with invalid Phone number is not created\r\n\r\n## Actual Result:\r\nWS chat with invalid Phone number is created\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/5a47eb49-3cfb-4446-8a6d-69da70913a7e\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021849978282313703906</li>\n        <li>Upwork Job ID: 1849978282313703906</li>\n        <li>Last Price Increase: 2024-10-26</li>\n<li>Automatic offers: </li>\n<ul>\n<li>akinwale | Reviewer | 104685790</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @stephanieelliott</details>", "_issue_id": "51316"}