{"price": 250.0, "title": "[HOLD for payment 2024-12-17] [$250] Search - When plenty of expenses are created already, haven't created expenses message shown", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** V9. 0.51-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to https://staging.new.expensify.com/home\r\n2. Log in account which has more expenses\r\n3. Tap search at bottom\r\n4. Note in expenses section, lots of expenses created are displayed\r\n5. Tap on search icon on top\r\n6. Search any random text \"yin\"\r\n7. Tap on the search term shown in dropdown\r\n8. Note user navigated to page showing haven't created any expenses\r\n\r\n## Expected Result:\r\nWhen plenty of expenses are created already, haven't created expenses message must not be displayed.\r\n\r\n## Actual Result:\r\nWhen plenty of expenses are created already, haven't created expenses message is displayed.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/8f4095d8-61f4-4fab-affa-a628f5a107f6\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848389047150206359</li>\n        <li>Upwork Job ID: 1848389047150206359</li>\n        <li>Last Price Increase: 2024-10-28</li>\n<li>Automatic offers: </li>\n<ul>\n<li>FitseTLT | Contributor | 104700929</li>\n</ul></li>\n    </ul>\n</details>\n\n\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @isabelastisser</details>", "_issue_id": "51168"}