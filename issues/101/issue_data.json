{"price": 125.0, "title": "[HOLD for payment 2025-01-02][$125] mWeb - Chat - Quote markdown is not copied in compose box", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** V9. 0.63-3\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n## Action Performed:\r\n1. Go to https://staging.new.expensify.com/home (login new gmail account)\r\n2. Open a self DM chat\r\n3. Enter > ga\r\n4. Send the message\r\n5. Long press the message sent and select copy to clipboard\r\n6. Paste the test in compose box\r\n7. Tap plus icon next to chat input\r\n8. Tap create expense\r\n9. Enter an amount and tap next\r\n10. In description, paste > ga\r\n\r\n## Expected Result:\r\nQuote markdown must be copied in compose box and description.\r\n\r\n## Actual Result:\r\nQuote markdown is not copied in compose box and description.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\nhttps://github.com/user-attachments/assets/e7696cae-b64d-42f0-9b70-a7ce2c26b829\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021860869478714981177</li>\n        <li>Upwork Job ID: 1860869478714981177</li>\n        <li>Last Price Increase: 2025-01-06</li>\n<li>Automatic offers: </li>\n<ul>\n<li>daledah | Contributor | 105112237</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @lschurr / @lschurr</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @RachCHopkins / @lschurr</details>", "_issue_id": "52681"}