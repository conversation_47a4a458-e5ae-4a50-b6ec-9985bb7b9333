{"price": 125.0, "title": "[$125] Categorize - Checkbox does not appear next to the categories in Categories RHP", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to workspace settings > Categories\r\n3. Disable all the categories\r\n4. Go to self DM\r\n5. Track a manual expense\r\n6. Click Categorize it from the actionable whisper\r\n7. Select the workspace with all the categories disabled\r\n\r\n## Expected Result:\r\nCheckbox will appear next to the categories in Categories RHP so that user can bulk select all the categories\r\n\r\n## Actual Result:\r\nCheckbox does not appear next to the categories in Categories RHP\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/5c22d5dc-2802-4ac3-b6b2-4da7c468de75\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854581870980912212</li>\n        <li>Upwork Job ID: 1854581870980912212</li>\n        <li>Last Price Increase: 2024-11-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>neonbhai | Contributor | 105008672</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @mananjadhav</details>", "_issue_id": "52131"}