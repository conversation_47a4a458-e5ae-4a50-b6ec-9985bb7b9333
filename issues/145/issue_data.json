{"price": 250.0, "title": "[HOLD for payment 2024-11-20] [$250] Distance-Distance receipt placeholder icon is hardly visible when expense is submitted offline", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5183758\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to workspace chat\r\n3. Go offline\r\n4. Submit a distance expense\r\n5. Go to transaction thread\r\n\r\n## Expected Result:\r\nThe distance receipt placeholder icon will be visible when the expense is submitted offline\r\n\r\n## Actual Result:\r\nThe distance receipt placeholder icon is hardly visible when the expense is submitted offline\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/25e64139-7734-40a8-841d-f79847da7ca5\r\n\r\n<img width=\"1440\" alt=\"40 555\" src=\"https://github.com/user-attachments/assets/f15bf8f7-ecc7-45ef-9296-bbf5c2c3350a\">\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854203499700129961</li>\n        <li>Upwork Job ID: 1854203499700129961</li>\n        <li>Last Price Increase: 2024-11-06</li>\n<li>Automatic offers: </li>\n<ul>\n<li>FitseTLT | Contributor | 104805961</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @puneetlath</details>", "_issue_id": "52123"}