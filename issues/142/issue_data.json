{"price": 250.0, "title": "[HOLD for payment 2024-12-05] [$250]  Tags - State field is not auto selected when there is only one tag", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5188086\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- Workspace is set up with dependent multi tags (use the file attached below).\r\n\r\n1. Launch hybrid or ND app.\r\n2. Go to workspace chat in which dependent multi tags are set up.\r\n3. Tap + > Submit expense > Manual.\r\n4. Enter amount > Next.\r\n\r\n## Expected Result:\r\nState field will be auto selected with the only one tag because \"Members must tag all expenses\" in Tags settings is enabled. (It is auto selected on staging web and production hybrid).\r\n\r\n## Actual Result:\r\nState field is not auto selected with the only one tag even when \"Members must tag all expenses\" in Tags settings is enabled.\r\nThis issue only happens on Android and iOS app (both hybrid and standalone).\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/a69be543-24a0-43c4-b69c-8337d8ec2c3a\r\n\r\n[Bug6656808_1730914735568!Dependent_-_Multi_Level_tags_NEW.csv](https://github.com/user-attachments/files/17651632/Bug6656808_1730914735568.Dependent_-_Multi_Level_tags_NEW.csv)\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854535982635221591</li>\n        <li>Upwork Job ID: 1854535982635221591</li>\n        <li>Last Price Increase: 2024-11-14</li>\n<li>Automatic offers: </li>\n<ul>\n<li>truph01 | Contributor | 104908657</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @strepanier03</details>", "_issue_id": "52137"}