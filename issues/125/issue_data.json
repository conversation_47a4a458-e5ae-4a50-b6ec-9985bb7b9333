{"price": 250.0, "title": "[HOLD for payment 2024-11-29] [$250]  Search - The shortcut CTRL + K does not work with a user as employee in a WS", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.59-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5191872\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Sign in to the app with a new Gmail account\r\n2. Choose \"Get paid back by my employer\" from the onboarding tasks\r\n3. Type a Name and Last name and click the \"Continue\" button\r\n4. Go to Settings, create a new workspace and invite a new account as an employee\r\n5. Log in with the new employee's account in incognito mode\r\n6. Press the shortcut CTRL + K to open search dialog\r\n\r\n## Expected Result:\r\nThe shortcut CTRL + K to open the search dialog should open correctly\r\n\r\n## Actual Result:\r\nPressing the combination CTRL + K to open the search dialog does not execute, while the other shortcuts such as CTRL + J and CTRL + D work correctly\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/0885fd7c-3775-4f18-b2d0-a214f3641150\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854902169710515284</li>\n        <li>Upwork Job ID: 1854902169710515284</li>\n        <li>Last Price Increase: 2024-11-13</li>\n<li>Automatic offers: </li>\n<ul>\n<li>dukenv0307 | Contributor | 104806147</li>\n<li>truph01 | Contributor | 104867213</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @abekkala</details>", "_issue_id": "52260"}