{"price": 250.0, "title": "[HOLD for payment 2024-12-07] [$250] Clean-up in the `Categorize it` flow", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.59-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @trjExpensify \r\n**Slack conversation** https://expensify.slack.com/archives/C07HPDRELLD/p1731032656572279?thread_ts=**********.304139&cid=C07HPDRELLD\r\n\r\n## Action Performed:\r\n\r\n**Case: Is not a member of `collect` or `control` workspaces**\r\n1. Sign-up for a new account \r\n2. Choose \"Track my personal spend\" > complete onboarding\r\n3. Go to the selfDM > `+` icon > Track expense\r\n4. Create a manual expense \r\n5. Click `Categorize it` \r\n\r\n**Case: Is a member of `collect` or `control` workspaces**\r\n1. Sign-up for a new account \r\n2. Choose \"Manage my team's expenses\" > complete onboarding\r\n3. Go to the selfDM > `+` icon > Track expense\r\n4. Create a manual expense \r\n5. Click `Categorize it` \r\n\r\n## Expected Result:\r\n_This is considered a feature request as it's improvements to the flow._ \r\n\r\n1. When you click `Categorize it`, if your \"default workspace\" (`activePolicyID`) is type `collect` or `control`, skip the selector and show the categories list of the default workspace. \r\n\r\n<img width=\"508\" alt=\"image\" src=\"https://github.com/user-attachments/assets/143742c4-d84f-44c6-a61a-37913953c8b4\">\r\n\r\n2. If you have no workspaces (or your default workspace is a personal policy), show an upgrade interstitial. When `Upgrade` is clicked, create a `collect` workspace. When `Got it, thanks` is clicked, move on to show the list of categories on the workspace created. \r\n\r\n<img width=\"860\" alt=\"image\" src=\"https://github.com/user-attachments/assets/999d33a0-c5f7-4b0e-aaa0-d835ca946738\">\r\n\r\n## Actual Result:\r\n1. When the user has workspaces, we show a selector to enter a name, email or phone number or create a new workspace. It's incredibly easy to go wrong here and create a second workspace, as evident in the thread. \r\n\r\n<img width=\"944\" alt=\"image\" src=\"https://github.com/user-attachments/assets/7dc3f6b9-232a-486d-a236-fa03d3b7124b\">\r\n\r\n2. When the user has no workspaces, we seem to be showing an empty state for the submit expense flow which doesn't make sense or match the action you just took to \"categorize it\" not \"submit it to someone\"\r\n\r\n<img width=\"1511\" alt=\"image\" src=\"https://github.com/user-attachments/assets/9c8508f7-023e-4cb5-8d02-94353726758a\">\r\n\r\n## Workaround:\r\nN/A \r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\nIn-line above. \r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\r\n<details><summary>Upwork Automation - Do Not Edit</summary>\r\n    <ul>\r\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856320183270003796</li>\r\n        <li>Upwork Job ID: 1856320183270003796</li>\r\n        <li>Last Price Increase: 2024-11-12</li>\n<li>Automatic offers: </li>\n<ul>\n<li>nkdengineer | Contributor | 104948536</li>\n</ul></li>\r\n    </ul>\r\n</details>\r\n\r\n<details><summary>Issue Owner</summary>Current Issue Owner: @trjExpensify</details>", "_issue_id": "52253"}