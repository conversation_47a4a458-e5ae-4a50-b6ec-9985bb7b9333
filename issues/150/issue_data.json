{"price": 250.0, "title": "[HOLD for payment 2024-11-14] [HOLD for payment 2024-11-13] [$250] Bank account - App crashes when connecting to bank account", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.57-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to Workspace settings > Workflows.\r\n3. Click Connect bank account.\r\n4. Click Update to USD.\r\n\r\n## Expected Result:\r\nApp will not crash when connecting to bank account.\r\n\r\n## Actual Result:\r\nApp crashes when connecting to bank account.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n[0411_2.txt](https://github.com/user-attachments/files/********/0411_2.txt)\r\n\r\nhttps://github.com/user-attachments/assets/8a9f0e30-a382-412f-acf0-7b1adacdc431\r\n\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021853501012094691283</li>\n        <li>Upwork Job ID: 1853501012094691283</li>\n        <li>Last Price Increase: 2024-11-04</li>\n<li>Automatic offers: </li>\n<ul>\n<li>DylanDylann | Reviewer | 104737674</li>\n<li>CyberAndrii | Contributor | 104737676</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @muttmuure</details>", "_issue_id": "51961"}