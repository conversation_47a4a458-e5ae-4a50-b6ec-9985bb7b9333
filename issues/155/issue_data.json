{"price": 250.0, "title": "[PAID] [$250] Distance rate- WS owner can create multiple distance rate with same amount", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.55-9\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\nPreconditions\r\nNavigate to http://www.staging.new.expensify.com/\r\n1. Create a new Workspace\r\n2. Navigate to workspace editor\r\n3. Navigate to \"More features\"\r\n4. Enable the distance rates toggle\r\nSteps\r\n1. Navigate to distance rates\r\n2. Click on \"Add rate\"\r\n3. Enter any rate that includes a decimal (e.g 2.2) and save\r\n4. Enter the same rate as step 3 and save\r\n\r\n## Expected Result:\r\nError shows as the rate is already exist\r\n\r\n## Actual Result:\r\nWS owner can create multiple distance rate with same amount\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/980281ff-2eab-43ff-9902-ed8f6b9db5af\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854599760824128596</li>\n        <li>Upwork Job ID: 1854599760824128596</li>\n        <li>Last Price Increase: 2024-11-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>DylanDylann | Reviewer | 105060475</li>\n<li>Krishna2323 | Contributor | 105060479</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @strepanier03</details>", "_issue_id": "51769"}