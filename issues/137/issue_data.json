{"price": 250.0, "title": "[HOLD for payment 2024-11-20] [$250] mweb - Per diem - View your subscription link opens Subscription page then back to Workspace", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.59-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Create a new workspace\r\n3. Go to More features\r\n4. Enable Per diem\r\n5. Tap Upgrade\r\n6. On upgrade success screen, tap View your subscription\r\n\r\n## Expected Result:\r\nApp will open subscription page\r\n\r\n## Actual Result:\r\nApp opens subscription page and then opens workspace editor page quickly.\r\nThis issue only happens when upgrading workspace to Control from Per diem feature\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/bcdd8673-64a7-46ba-af35-d9ce8c50b4b1\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854641998677095618</li>\n        <li>Upwork Job ID: 1854641998677095618</li>\n        <li>Last Price Increase: 2024-11-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>truph01 | Contributor | 104804112</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @sakluger</details>", "_issue_id": "52201"}