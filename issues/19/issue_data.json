{"price": 250.0, "title": "[HOLD for payment 2025-01-28] [$250] Improve visibility of tooltips", "issue_repo_steps": "**Problem:** Our current educational tooltips use a very small pointer arrow, and they have very low contrast with the background color of the app in light mode. As a result, it's quite hard to tell exactly what a tooltip is pointing at.\r\n\r\n**Solution:** Let's update our tooltips to use a higher-contrast BG color, use a larger pointer, and make them theme-dependent. The result would be something like this:\r\n![image](https://github.com/user-attachments/assets/8b2360ad-6bca-4e85-98bd-0c3c37476bca)\r\n\r\n**Light mode:**\r\n\r\n- BG: green700\r\n- icon: green400\r\n- text: textReversed (aka text color we use for dark mode)\r\n\r\n**Dark mode:**\r\n\r\n- BG: green200\r\n- icon: green 400\r\n- text: textReversed (aka text color we use for light mode)\r\n\r\n**Pointer:**\r\n- New size should be 16px wide by 8px tall\r\n\r\n\r\nConvo: https://expensify.slack.com/archives/C07HPDRELLD/p1735250426325589\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876672988734265232</li>\n        <li>Upwork Job ID: 1876672988734265232</li>\n        <li>Last Price Increase: 2025-01-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>ishpaul777 | Reviewer | 105640697</li>\n<li>truph01 | Contributor | 105640699</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @garrettmknight</details>", "_issue_id": "54775"}