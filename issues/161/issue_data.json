{"price": 250.0, "title": "[HOLD for payment 2025-01-13] [$250] Add a more inviting sign-off from Concierge after our onboarding messages so it looks good in the LHN", "issue_repo_steps": "**Slack thread**\r\nhttps://expensify.slack.com/archives/C07HPDRELLD/p1729810967369919\r\n\r\n**Problem**\r\nFor new users viewing the LHN, we draw attention to their Concierge chat with a GBR and \"Get started here!\" tooltip, which is great. However, the message preview from Concierge shows a task to complete depending on your onboarding intent selection. This is subjective, but it doesn't feel very welcoming/inviting, and also a bit strange and random.\r\n\r\n![image (97)](https://github.com/user-attachments/assets/6e98c96f-36dd-4701-b3ed-d5bd49f79d90)\r\n\r\n**Solution**\r\nAdd a new line `It's great to meet you!` right below the onboarding tasks/message from Concierge so it shows as the preview text from Concierge in the LHN. \r\n\r\n<details><summary>Upwork Automation - Do Not Edit</summary>\r\n    <ul>\r\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021850951668861078961</li>\r\n        <li>Upwork Job ID: 1850951668861078961</li>\r\n        <li>Last Price Increase: 2024-12-06</li>\r\n<li>Automatic offers: </li>\r\n<ul>\r\n<li>shubham1206agra | Contributor | 104743508</li>\r\n</ul></li>\r\n    </ul>\r\n</details>\r\n\r\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @lschurr</details>", "_issue_id": "51501"}