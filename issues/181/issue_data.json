{"price": 250.0, "title": "[HOLD for payment 2024-11-11] [$250] Invoices - Invoice preview sender appears and disappears when sending invoices consecutively", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.51-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** https://expensify.testrail.io/index.php?/tests/view/5102388\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to FAB > Send invoices.\r\n3. Send an invoice to anyone.\r\n4. In the invoice chat, click + > Send invoice.\r\n5. Send another invoice.\r\n\r\n## Expected Result:\r\nThe invoice preview sender will not appear and disappear when sending invoices consecutively.\r\n\r\n## Actual Result:\r\nThe invoice preview sender appears then disappears when sending invoices consecutively.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/e4d15c1c-5317-4af3-8558-2308e62cc7cf\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848369344890325291</li>\n        <li>Upwork Job ID: 1848369344890325291</li>\n        <li>Last Price Increase: 2024-10-21</li>\n<li>Automatic offers: </li>\n<ul>\n<li>hoangzinh | Reviewer | 104579791</li>\n<li>NJ-2020 | Contributor | 104579793</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @slafortune</details>", "_issue_id": "51128"}