{"price": 75.0, "title": "[HOLD for payment 2024-11-13] [$75] Add a divider between the default spend categories in category settings", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.52-2\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** @trjExpensify \r\n\r\n## Action Performed:\r\n1. Create a workspace \r\n2. Go to the Categories tab of the workspace settings\r\n3. Click the Settings button in the header\r\n\r\n## Expected Result:\r\n_Note: This is an improvement, not a regression._  \r\n\r\n- Add a divider between the two settings, like so: \r\n\r\n![image (20)](https://github.com/user-attachments/assets/76a8473d-5f82-4ce0-a444-516676dd6ce2)\r\n\r\nCC: @dannymcclain for putting together that mockaroo. \r\n\r\n## Actual Result:\r\nThere's little to no space between the two different settings on this screen right now, which makes it seem like they are related to each other but they're actually independent settings:\r\n\r\n<img width=\"1511\" alt=\"image\" src=\"https://github.com/user-attachments/assets/f1bc1af8-04b0-4dfd-a65e-9965e20660a2\">\r\n\r\n\r\n## Workaround:\r\nN/A, visual improvement. \r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\nIn-line above. \r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848766520698079085</li>\n        <li>Upwork Job ID: 1848766520698079085</li>\n        <li>Last Price Increase: 2024-10-22</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkzie2 | Contributor | 104572581</li>\n</ul></li>\n    </ul>\n</details>\n\n\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @trjExpensify</details>", "_issue_id": "51269"}