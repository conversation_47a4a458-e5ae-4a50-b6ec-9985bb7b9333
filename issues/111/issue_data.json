{"price": 250.0, "title": "[HOLD for payment 2024-12-05] [$250] Search - Expenses change position briefly when switching from Settings to Search", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.63-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- Have many expenses\r\n1. Go to staging.new.expensify.com\r\n2. Go to Search\r\n3. Change sorting a few times (click Merchant, From, To etc)\r\n4. Click Settings\r\n5. Go back to Search\r\n6. Might need to repeat Step 4 and 5 a few times to see the expenses changing position briefly\r\n\r\n## Expected Result:\r\nThe expenses will not change position briefly when switching from Settings to Search after changing sorting order.\r\n\r\n## Actual Result:\r\nThe expenses change position briefly when switching from Settings to Search after changing sorting order.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/014ee706-3257-4095-9ee9-7728cfcdd733\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858539185487721038</li>\n        <li>Upwork Job ID: 1858539185487721038</li>\n        <li>Last Price Increase: 2024-11-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>FitseTLT | Contributor | 105027229</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @isabelastisser</details>", "_issue_id": "52632"}