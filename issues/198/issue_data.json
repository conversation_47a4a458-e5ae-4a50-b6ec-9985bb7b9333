{"price": 250.0, "title": "[HOLD for payment 2024-11-21] [$250] Inbox showing GBR when there’s a report with RBR", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.49-0\r\n**Reproducible in staging?:** y\r\n**Reproducible in production?:** y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @pac-guerreiro \r\n**Slack conversation:** https://expensify.slack.com/archives/C049HHMV9SM/p1728665408536049\r\n\r\n## Action Performed:\r\n\r\n1. Make sure there is at least a report with GBR and another with a RBR\r\n2. Select a report which hasn’t a RBR indicator\r\n3. Go to Troubleshoot, then click on Clear cache and restart\r\n4. Go back to Inbox and check that the Inbox is showing a GBR indicator\r\n5. Open the report that has the RBR indicator then refresh the page\r\n\r\n## Expected Result:\r\nShould have same logic that should display RBR in inbox\r\n\r\n## Actual Result:\r\nThe inbox indicator doesn’t show an updated color until the page is refreshed and it shows the wrong color until a report is fully loaded\r\n\r\n## Workaround:\r\nunknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/287d512e-03f5-4ff2-80fd-8e590c91596e\r\n\r\n\r\nhttps://github.com/user-attachments/assets/791a3300-0f7a-49dc-b7f5-f1d004ce0958\r\n\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021847128366508796109</li>\n        <li>Upwork Job ID: 1847128366508796109</li>\n        <li>Last Price Increase: 2024-10-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkhutornyi | Reviewer | 104590221</li>\n<li>truph01 | Contributor | 104590223</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @alexpensify</details>", "_issue_id": "50927"}