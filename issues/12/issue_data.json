{"price": 250.0, "title": "[HOLD for payment 2025-01-28] [$250] Per diem - App still shows old subrate and quantity after changing subrate and quantity to new value", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.81-1\r\n**Reproducible in staging?:** Yes\r\n**Reproducible in production?:** Unable to check\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n**Device used:** Mac 15.0 / Chrome\r\n**App Component:** Money Requests\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- Workspace has per diem rates.\r\n\r\n1. Go to staging.new.expensify.com\r\n2. Go to workspace chat.\r\n3. Click + > Submit expense > Per diem.\r\n4. Select a country, select valid date and time and click Save.\r\n5. Select a subrate and quantity > Save\r\n6. On confirmation page, click on the subrate field.\r\n7. Change to a new subrate and quantity > Save.\r\n8. On confirmation page, click RHP back button.\r\n\r\n## Expected Result:\r\nSince the subrate and quantity is changed to new value in Step 7, app should display the new subrate and quantity when going back from confirmation page to subrate page.\r\n\r\n## Actual Result:\r\nWhen subrate and quantity is changed to new value in Step 7, app still displays the old value in Step 5 when returning to subrate page from confirmation page.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n- [ ] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/08aecc97-d7e3-40aa-824e-93a2f0e843a9\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876687121588096404</li>\n        <li>Upwork Job ID: 1876687121588096404</li>\n        <li>Last Price Increase: 2025-01-07</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @joekaufmanexpensify</details>", "_issue_id": "54884"}