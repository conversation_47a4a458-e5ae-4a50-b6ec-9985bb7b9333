{"price": 250.0, "title": "[HOLD for payment 2024-12-20] [$250]  Inbox LHN wrapper is missing 12px of top padding", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.59-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @shawnborton \r\n**Slack conversation** (hyperlinked to channel name): [ts_external_expensify_bugs](https://expensify-ts.slack.com/archives/C049HHMV9SM/p1730970240658609)\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2.  Observe LHN padding\r\n## Expected Result:\r\n12px padding for the wrapper that wraps LHN items\r\n## Actual Result:\r\nInbox doesn't have this same top padding.\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n![CleanShot 2024-11-07 at 10 04 39@2x](https://github.com/user-attachments/assets/fa72e7e5-7b2e-4935-9f7b-7e1498c82b09)\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021857045218853656773</li>\n        <li>Upwork Job ID: 1857045218853656773</li>\n        <li>Last Price Increase: 2024-11-21</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkzie2 | Contributor | 105086530</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @greg-schroeder / @greg-schroeder</details>", "_issue_id": "52198"}