{"price": 250.0, "title": "[HOLD for payment 2024-11-21] [$250] mWweb - Chat - From search page, tapping on image doesn't show image", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** V9. 0.51-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to https://staging.new.expensify.com/home\r\n2. Tap on a chat\r\n3. Pate the text in compose box - *test* ![demo image](https://picsum.photos/id/1067/200/300)_test_\r\n4. Send the message\r\n5. Navigate to LHN\r\n6. Tap bottom search icon\r\n7. From the dropdown, navigate to the chat\r\n8. Tap on the image\r\n\r\n## Expected Result:\r\nFrom search page, tapping on image must show image.\r\n\r\n## Actual Result:\r\nFrom search page, tapping on image doesn't show image.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [ ] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/4de7338d-6f69-44b9-812f-cc20654af9a2\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021848845902924647302</li>\n        <li>Upwork Job ID: 1848845902924647302</li>\n        <li>Last Price Increase: 2024-10-29</li>\n<li>Automatic offers: </li>\n<ul>\n<li>huult | Contributor | 104681846</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @stephanieelliott / @stephanieelliott</details>", "_issue_id": "51161"}