{"price": 250.0, "title": "[HOLD for payment 2024-11-21] [$250] Web - Company cards - Undefined cards is displayed on card name when refreshing the page", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.58-0 \r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Create a workspace > click on More features > enable Company cards\r\n2. Add a company card\r\n3. Click on 'Settings' button on the newly added company card\r\n4. Click 'Card feed name'\r\n5. Refresh the page\r\n\r\n## Expected Result:\r\nThe card name should be displayed\r\n\r\n## Actual Result:\r\n`Undefined cards` is displayed\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/cc965353-e842-4215-b6be-3cd192724651\r\n\r\n \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~02*********7067768118</li>\n        <li>Upwork Job ID: *********7067768118</li>\n        <li>Last Price Increase: 2024-11-08</li>\n<li>Automatic offers: </li>\n<ul>\n<li>DylanDylann | Reviewer | 104837075</li>\n<li>huult | Contributor | 104837077</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @twisterdotcom</details>", "_issue_id": "52140"}