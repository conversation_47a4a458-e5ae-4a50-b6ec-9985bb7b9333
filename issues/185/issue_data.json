{"price": 250.0, "title": "[HOLD for payment 2024-11-05] [$250] Some table rows have `>` right caret", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.50-0\r\n**Reproducible in staging?:** y\r\n**Reproducible in production?:** y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @shawnborton \r\n**Slack conversation:** https://expensify.slack.com/archives/C049HHMV9SM/p1729177128009089\r\n\r\n## Action Performed:\r\n1. Open the workspace members page, Expenses page\r\n2. Open categories and tags pages\r\n## Expected Result:\r\nShould not have `>` at the right\r\n## Actual Result:\r\nRight now some table rows use a right caret (>) at the end of the row, whereas others don't. \r\nIt's pretty obvious that we can tap any table row, so all of the carets from any table rows should be removed\r\n## Workaround:\r\nunknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [x] iOS: Standalone\r\n- [x] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n![CleanShot 2024-10-17 at 17 03 20@2x](https://github.com/user-attachments/assets/9b5c4cdd-7dd5-4f50-9a7e-8b532a856381)\r\n![CleanShot 2024-10-17 at 17 03 08@2x](https://github.com/user-attachments/assets/a4ebcbe3-4e56-4a33-b1db-d4ac434d54ba)\r\n![CleanShot 2024-10-17 at 17 02 56@2x](https://github.com/user-attachments/assets/a647692d-8bda-4171-80c9-4736b0067d0a)\r\n\r\nhttps://github.com/user-attachments/assets/d42ae5bd-416d-48e9-a943-60c0b80208db\r\n\r\n\r\n  \r\n![CleanShot 2024-10-17 at 16 58 56@2x](https://github.com/user-attachments/assets/6b3cd28f-dc93-417f-9c75-0249dc03b104)\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021847349478186381538</li>\n        <li>Upwork Job ID: 1847349478186381538</li>\n        <li>Last Price Increase: 2024-10-18</li>\n<li>Automatic offers: </li>\n<ul>\n<li>ChavdaSachin | Contributor | 104524082</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @slafortune</details>", "_issue_id": "51053"}