{"price": 250.0, "title": "[HOLD for payment 2024-12-05] [$250] Track expense - Back button does not return to confirmation page after clicking workspace name", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.60-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause - Internal Team\r\n\r\n\r\n## Action Performed:\r\nPrecondition:\r\n- User has at least one workspace\r\n1. Go to staging.new.expensify.com\r\n2. Go to self DM\r\n3. Track a manual expense\r\n4. Go to Search\r\n5. Click on the track expense\r\n6. Click on the report header\r\n7. Click Share it with my accountant\r\n8. Select a workspace\r\n9. On confirmation page, click on the workspace under \"To\"\r\n10. Click RHP back button\r\n\r\n## Expected Result:\r\nApp will return to confirmation page\r\n\r\n## Actual Result:\r\nApp returns to expense details page\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [x] Android: Standalone\r\n- [x] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details> \r\n\r\nhttps://github.com/user-attachments/assets/f7c3216b-9bee-45af-b5d3-0f5f884a6ee8\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021856400727068529748</li>\n        <li>Upwork Job ID: 1856400727068529748</li>\n        <li>Last Price Increase: 2024-11-12</li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @isabelastisser</details>", "_issue_id": "52398"}