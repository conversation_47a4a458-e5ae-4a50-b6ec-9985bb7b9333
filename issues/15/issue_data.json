{"price": 125.0, "title": "[HOLD for payment 2025-01-18] [$125] card-No space between \"other\" card feed and next button", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** v9.0.81-1\r\n**Reproducible in staging?:** Yes\r\n**Reproducible in production?:** Yes\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:** N/A\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:** N/A\r\n**Email or phone of affected tester (no customers):** N/A\r\n**Issue reported by:** Applause Internal Team\r\n**Device used:** Redmi note 10s android 13\r\n**App Component:** Workspace Settings\r\n\r\n## Action Performed:\r\n1. Go to https://staging.new.expensify.com/home\r\n2. Tap settings -- workspace settings - more features\r\n3. Enable company cards\r\n4. Open company card\r\n5. Scroll down the page and select \"other\"\r\n\r\n## Expected Result:\r\nThere must be space between \"other\" card feed and next button.\r\n\r\n## Actual Result:\r\nThere is no space between \"other\" card feed and next button.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [x] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [x] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/1053b335-9341-42ca-9e7a-f4ad80fe8679\r\n\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021876632229679213396</li>\n        <li>Upwork Job ID: 1876632229679213396</li>\n        <li>Last Price Increase: 2025-01-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>nkdengineer | Contributor | 105595694</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @bfitzexpensify</details>", "_issue_id": "54856"}