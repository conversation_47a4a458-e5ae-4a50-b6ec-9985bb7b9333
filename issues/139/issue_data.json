{"price": 250.0, "title": "[HOLD for payment 2024-11-28] [$250] When a row is selected in the chat switcher, it doesn't have rounded edges", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.58-1\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @shawnborton \r\n**Slack conversation** (hyperlinked to channel name): [ts_external_expensify_bugs](https://expensify-ts.slack.com/archives/C049HHMV9SM/p1730819731974669)\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Click Search > chats\r\n3. Click chat finder and enter key word to search\r\n4. Use the arrow keys on the keyboard to select a search result\r\n## Expected Result:\r\nRow's selected style should have the rounded edges\r\n## Actual Result:\r\nRow's selected style doesn't have the rounded edges\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n  \r\n</details>\r\n\r\n![CleanShot 2024-11-05 at 16 14 33@2x](https://github.com/user-attachments/assets/c03f7528-2f75-44ec-92d1-a8ff90e345fe)\r\n\r\n![Snip - (1) New Expensify - Google Chrome](https://github.com/user-attachments/assets/37aefa4f-7116-4b2f-9f63-d64043b9c53c)\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021854327558552515894</li>\n        <li>Upwork Job ID: 1854327558552515894</li>\n        <li>Last Price Increase: 2024-11-07</li>\n<li>Automatic offers: </li>\n<ul>\n<li>rayane-djouah | Reviewer | 104845398</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @isabelastisser</details>", "_issue_id": "52158"}