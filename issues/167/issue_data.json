{"price": 250.0, "title": "[HOLD for payment 2024-11-14] [$250] Update all enable/disable workspace editor menu items to use more appropriate icons.", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.52-5\r\n**Reproducible in staging?:** Yes\r\n**Reproducible in production?:** Yes\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @dannymcclain \r\n**Slack conversation:** https://expensify.slack.com/archives/C049HHMV9SM/p1729696426720769\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Settings > Workspace\r\n3. Enable and disable distance rates, categories, tags, taxes, etc in the workspace editor\r\n## Expected Result:\r\nUpdate all of these options to use our checkmark icon for enabling and our close icon for disabling\r\n## Actual Result:\r\nFor enabling and disabling distance rates, categories, tags, taxes, etc in the workspace editor, we use the document slash and document icons\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n![Full Screen #16](https://github.com/user-attachments/assets/27c541ca-a8e5-4c2e-804e-809e403842b0)\r\n  \r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021850952147214906851</li>\n        <li>Upwork Job ID: 1850952147214906851</li>\n        <li>Last Price Increase: 2024-10-28</li>\n<li>Automatic offers: </li>\n<ul>\n<li>klajdipaja | Contributor | 104664755</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @zanyrenney</details>", "_issue_id": "51361"}