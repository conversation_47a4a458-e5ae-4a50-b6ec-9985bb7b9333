{"price": 250.0, "title": "[PAID][HOLD for payment 2024-12-06] [$250] Update workspace switcher to look and act more like a filter", "issue_repo_steps": "## Background\r\nComing from [this long convo](https://expensify.slack.com/archives/C03U7DCU4/p1731536477731219), we'd like to update the workspace switcher to look and act more like a filter.\r\n\r\n## Problem\r\nThe current workspace chat switcher has the \"Everything\" > \"Expensify\" selection as its default, which is unnecessary in general (especially when you don't have any workspaces) and takes up valuable real estate. \r\n\r\n## Solution\r\n- Remove the \"Everything\" > \"Expensify\" section of the workspace switcher completely. Showing everything both on and off workspace will just be the default of the product.\r\n- Since showing everything both on and off workspace will now be the default of the product, the workspace switcher should default to **no** workspaces selected in the switcher (which means it's showing everything).\r\n  - The closed state of the switch should appear the same as it does now when \"Everything > Expensify\" is selected.\r\n- Selecting a given workspace will filter only for that workspace.\r\n  - The closed state of the switcher should appear the same as it does now when a workspace is selected.\r\n- Note: If multiple workspaces exist, only allow for filtering on one workspace at a time. Don't allow multi-select.\r\n\r\n## Screenshots\r\nHere's what we want the finished product to look like:\r\n![CleanShot 2024-11-15 at 13 43 23@2x](https://github.com/user-attachments/assets/5378590d-9c85-46b8-9432-1ccde238cf1c)\r\n\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021858848669430376492</li>\n        <li>Upwork Job ID: 1858848669430376492</li>\n        <li>Last Price Increase: 2024-11-19</li>\n<li>Automatic offers: </li>\n<ul>\n<li>paultsimura | Reviewer | 105060504</li>\n<li>Krishna2323 | Contributor | 105060505</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @CortneyOfstad</details>", "_issue_id": "52654"}