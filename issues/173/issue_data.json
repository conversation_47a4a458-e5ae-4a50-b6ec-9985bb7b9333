{"price": 125.0, "title": "[HOLD for payment 2024-11-29] [$125] Copilot - Clicking outside magic code RHP does not dismiss the RHP entirely", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.52-2\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** N\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Issue reported by:** Applause Internal Team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Go to Account settings > Security.\r\n3. Click Add copilot.\r\n4. Select a user > Next.\r\n5. Select an access level > Next.\r\n6. Click Add copilot.\r\n7. Click outside the RHP to dismiss it.\r\n\r\n## Expected Result:\r\nThe RHP will be dismissed when clicking outside it for the first time (production behavior).\r\n\r\n## Actual Result:\r\nThe RHP is not dismissed when clicking outside it for the first time. It returns to copilot confirmation page.\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [x] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\nhttps://github.com/user-attachments/assets/f1f8540d-f066-4bcf-ab49-22278b208943\r\n\r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021850957170859734764</li>\n        <li>Upwork Job ID: 1850957170859734764</li>\n        <li>Last Price Increase: 2024-11-04</li>\n<li>Automatic offers: </li>\n<ul>\n<li>mkzie2 | Contributor | 104756037</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @sobitneupane</details>", "_issue_id": "51272"}