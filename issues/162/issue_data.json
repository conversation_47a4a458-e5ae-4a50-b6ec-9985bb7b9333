{"price": 250.0, "title": "[PAID] [$250] Composer - <PERSON><PERSON><PERSON> error shows up when pasting the same text in the composer", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:** 9.0.54-0\r\n**Reproducible in staging?:** Y\r\n**Reproducible in production?:** Y\r\n**Email or phone of affected tester (no customers):** <EMAIL>\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Issue reported by:** Applause-Internal team\r\n\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Type anything in the composer.\r\n3. Highlight all the content.\r\n4. Copy the content.\r\n5. While the content is still highlighted, paste the same content.\r\n\r\n## Expected Result:\r\nNo console error will show up.\r\n\r\n## Actual Result:\r\nConsole error shows up when pasting the same text in the composer\r\n\r\n## Workaround:\r\nUnknown\r\n\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n\r\nhttps://github.com/user-attachments/assets/2951a5a3-55b3-4621-ad05-a41e6e42d001\r\n\r\n[Bug6645374_1729849788357!staging.new.expensify.com-1729840230855.txt](https://github.com/user-attachments/files/17522348/Bug6645374_1729849788357.staging.new.expensify.com-1729840230855.txt)\r\n\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021851405996383084182</li>\n        <li>Upwork Job ID: 1851405996383084182</li>\n        <li>Last Price Increase: 2024-10-29</li>\n<li>Automatic offers: </li>\n<ul>\n<li>jjcoffee | Reviewer | 104665940</li>\n<li>nkdengineer | Contributor | 104665943</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @strepanier03</details>", "_issue_id": "51466"}