{"price": 250.0, "title": "[HOLD for payment 2024-11-18] [$250] Update the icon size in icon-only buttons to match the icon size in our regular buttons", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:**\r\n**Reproducible in staging?:**\r\n**Reproducible in production?:**\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:** @dannymcclain \r\n**Slack conversation:** https://expensify.slack.com/archives/C049HHMV9SM/p1729695613695369\r\n\r\n## Action Performed:\r\n1. Go to staging.new.expensify.com\r\n2. Observe the icon-only buttons\r\n## Expected Result:\r\n Update the icon size in icon-only buttons to use the same size\r\n## Actual Result:\r\nCurrently, the icons in icon-only buttons are 4px larger than in our regular buttons of the same size\r\n## Workaround:\r\nUnknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n![image](https://github.com/user-attachments/assets/8c2a7c2a-b616-4378-93fb-10fb89b594f3)\r\n\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\n<details><summary>Upwork Automation - Do Not Edit</summary>\n    <ul>\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021849243090532893090</li>\n        <li>Upwork Job ID: 1849243090532893090</li>\n        <li>Last Price Increase: 2024-10-24</li>\n<li>Automatic offers: </li>\n<ul>\n<li>rojiphil | Reviewer | 104658121</li>\n</ul></li>\n    </ul>\n</details>\n\n<details><summary>Issue Owner</summary>Current Issue Owner: @twisterdotcom</details>", "_issue_id": "51355"}