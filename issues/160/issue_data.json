{"price": 250.0, "title": "[HOLD for payment 2024-11-13] [$250] Clicking \" Please sign in again\" does nothing in \"Session Expired\" page", "issue_repo_steps": "If you haven’t already, check out our [contributing guidelines](https://github.com/Expensify/ReactNativeChat/blob/main/contributingGuides/CONTRIBUTING.md) for onboarding <NAME_EMAIL> to request to join our Slack channel!\r\n___\r\n\r\n**Version Number:**\r\n**Reproducible in staging?:** needs reproduction\r\n**Reproducible in production?:** needs reproduction\r\n**If this was caught on HybridApp, is this reproducible on New Expensify Standalone?:**\r\n**If this was caught during regression testing, add the test name, ID and link from TestRail:**\r\n**Email or phone of affected tester (no customers):**\r\n**Logs:** https://stackoverflow.com/c/expensify/questions/4856\r\n**Expensify/Expensify Issue URL:**\r\n**Issue reported by:**  @carlosmiceli \r\n**Slack conversation:** https://expensify.slack.com/archives/C049HHMV9SM/p1729880182560339\r\n\r\n## Action Performed:\r\n1. Have a \" Your Session has Expired\"  screen\r\n2. Click on \"Please sign in again\"\r\n## Expected Result:\r\nUser is taken to sign in screen\r\n## Actual Result:\r\nNothing happens \r\n## Workaround:\r\nunknown\r\n## Platforms:\r\n<!---\r\nCheck off any platforms that are affected by this issue\r\n--->\r\nWhich of our officially supported platforms is this issue occurring on?\r\n- [ ] Android: Standalone\r\n- [ ] Android: HybridApp\r\n- [ ] Android: mWeb Chrome\r\n- [ ] iOS: Standalone\r\n- [ ] iOS: HybridApp\r\n- [ ] iOS: mWeb Safari\r\n- [x] MacOS: Chrome / Safari\r\n- [ ] MacOS: Desktop\r\n\r\n## Screenshots/Videos\r\n\r\n<details>\r\n  <summary>Add any screenshot/video evidence</summary>\r\n\r\n![Screenshot 2024-10-25 at 3 14 04 PM](https://github.com/user-attachments/assets/a0962256-b82f-4024-b3e0-13dc97578294)\r\n\r\n  \r\n</details>\r\n\r\n[View all open jobs on GitHub](https://github.com/Expensify/App/issues?q=is%3Aopen+is%3Aissue+label%3A%22Help+Wanted%22)\r\n\r\n<details><summary>Upwork Automation - Do Not Edit</summary>\r\n    <ul>\r\n        <li>Upwork Job URL: https://www.upwork.com/jobs/~021851043610237148822</li>\r\n        <li>Upwork Job ID: 1851043610237148822</li>\r\n        <li>Last Price Increase: 2024-10-28</li>\n<li>Automatic offers: </li>\n<ul>\n<li>aimane-chnaif | Reviewer | 104689909</li>\n<li>NJ-2020 | Contributor | 104689912</li>\n</ul></li>\r\n    </ul>\r\n</details>\r\n\r\n<details><summary>Issue Owner</summary>Current Issue Owner: @CortneyOfstad</details>", "_issue_id": "51546"}